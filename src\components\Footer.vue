<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- Company Info -->
        <div class="footer-section company-info">
          <div class="footer-logo">
            <div class="logo-placeholder">CGC</div>
            <span>CGC DIGITAL</span>
          </div>
          <p class="company-description">
            CGC DIGITAL PROVIDES A VARIETY OF SERVICES COVERING THE LIFECYCLE OF
            YOUR PROJECT
          </p>
          <div class="social-links">
            <a href="#" class="social-link">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="social-link">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="social-link">
              <i class="fab fa-linkedin-in"></i>
            </a>
          </div>
          <div class="newsletter">
            <h4>Subscribe to our newsletter</h4>
            <div class="newsletter-form">
              <input type="email" placeholder="Enter your email address" />
              <button type="submit">Send</button>
            </div>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="footer-section">
          <h3>Quick Links</h3>
          <ul class="footer-links">
            <li><a href="#">SEACE</a></li>
            <li><a href="#">SEACE DIGITAL</a></li>
            <li><a href="#">Company</a></li>
          </ul>
        </div>

        <!-- Company Links -->
        <div class="footer-section">
          <h3>Company</h3>
          <ul class="footer-links">
            <li><a href="#">Company</a></li>
            <li><a href="#">Company</a></li>
          </ul>
        </div>

        <!-- Contacts -->
        <div class="footer-section contacts">
          <h3>Contacts</h3>
          <div class="contact-item">
            <div class="contact-icon email">
              <i class="fas fa-envelope"></i>
            </div>
            <span>*******@outlook.com</span>
          </div>
          <div class="contact-item">
            <div class="contact-icon phone">
              <i class="fas fa-phone"></i>
            </div>
            <div>
              <div>Technical Support</div>
              <div>0086+186****7876</div>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon phone">
              <i class="fas fa-phone"></i>
            </div>
            <div>
              <div>Sales and Business Inquiries</div>
              <div>0086+186****7876</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p>&copy; CGC DIGITAL &copy; All rights reserved.</p>
          <div class="footer-bottom-links">
            <a href="#">Terms of Service</a>
            <a href="#">Privacy Policy</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Footer component logic
</script>

<style scoped>
.footer {
  background: #2a2d35;
  color: #ffffff;
  padding: 60px 0 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #ffffff;
}

/* Company Info */
.company-info {
  max-width: 350px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.logo-placeholder {
  width: 40px;
  height: 40px;
  background: #00d4aa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  color: #ffffff;
}

.footer-logo span {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
}

.company-description {
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 30px;
  font-size: 0.9rem;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: #3a3d45;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  text-decoration: none;
  transition: background 0.3s ease;
}

.social-link:hover {
  background: #00d4aa;
}

.newsletter h4 {
  font-size: 1rem;
  margin-bottom: 15px;
  color: #ffffff;
}

.newsletter-form {
  display: flex;
  gap: 10px;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #4a4d55;
  border-radius: 5px;
  background: #3a3d45;
  color: #ffffff;
  font-size: 0.9rem;
}

.newsletter-form input::placeholder {
  color: #888;
}

.newsletter-form button {
  padding: 12px 20px;
  background: #e74c3c;
  color: #ffffff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.newsletter-form button:hover {
  background: #c0392b;
}

/* Footer Links */
.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #b0b0b0;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #00d4aa;
}

/* Contacts */
.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.contact-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon.email {
  background: #e74c3c;
}

.contact-icon.phone {
  background: #e74c3c;
}

.contact-item span,
.contact-item div {
  color: #b0b0b0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid #4a4d55;
  padding: 20px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #888;
  font-size: 0.9rem;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 30px;
}

.footer-bottom-links a {
  color: #888;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #00d4aa;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .newsletter-form {
    flex-direction: column;
  }
}
</style>

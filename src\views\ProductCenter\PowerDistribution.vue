<template>
  <div class="power-distribution">
    <!-- 主要内容区域 -->
    <div class="hero-section">
      <div class="container">
        <!-- 设备展示区 -->
        <div class="device-showcase">
          <div class="device-image">
            <div class="device-placeholder">
              <div class="device-icon">⚡</div>
            </div>
          </div>
          <h1 class="main-title">生命周期软硬件一体化协同</h1>
          <p class="main-description">
            基于物联网技术的智能配电监控系统，实现设备状态实时监测、
            故障预警、能耗分析等功能，为电力系统提供全方位的智能化管理解决方案。
          </p>
        </div>
      </div>
    </div>

    <!-- 标准流程说明 -->
    <div class="process-section">
      <div class="container">
        <div class="process-header">
          <h2>We always follow the standard work process</h2>
          <p>我们始终遵循标准化工作流程，确保产品质量和服务水准</p>
        </div>

        <div class="process-steps">
          <div class="step">
            <div class="step-icon">
              <span class="step-number">01</span>
            </div>
            <h4>需求分析</h4>
            <p>深入了解客户需求，制定个性化解决方案</p>
          </div>

          <div class="step">
            <div class="step-icon">
              <span class="step-number">02</span>
            </div>
            <h4>方案设计</h4>
            <p>基于需求分析结果，设计最优的技术方案</p>
          </div>

          <div class="step">
            <div class="step-icon">
              <span class="step-number">03</span>
            </div>
            <h4>产品实施</h4>
            <p>严格按照设计方案进行产品开发和部署</p>
          </div>

          <div class="step">
            <div class="step-icon">
              <span class="step-number">04</span>
            </div>
            <h4>测试验收</h4>
            <p>全面测试产品功能，确保达到预期效果</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品展示区域 -->
    <div class="product-gallery">
      <div class="container">
        <div class="gallery-header">
          <h2>产品展示</h2>
          <p>配电监控系统核心产品组件</p>
        </div>

        <div class="product-grid">
          <!-- 第一行产品 -->
          <div class="product-row">
            <div class="product-item large">
              <div class="product-placeholder">
                <span>主控设备</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>传感器A</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>传感器B</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>传感器C</span>
              </div>
            </div>
          </div>

          <!-- 第二行产品 -->
          <div class="product-row">
            <div class="product-item">
              <div class="product-placeholder">
                <span>控制器1</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>控制器2</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>控制器3</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>控制器4</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>控制器5</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>控制器6</span>
              </div>
            </div>
          </div>

          <!-- 第三行产品 -->
          <div class="product-row">
            <div class="product-item">
              <div class="product-placeholder">
                <span>模块A</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>模块B</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>模块C</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>模块D</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>模块E</span>
              </div>
            </div>
            <div class="product-item">
              <div class="product-placeholder">
                <span>模块F</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 配电监控页面逻辑
</script>

<style scoped>
.power-distribution {
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
  padding-top: 130px; /* 为主导航+二级导航预留空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-section {
  padding: 80px 0;
  text-align: center;
}

.device-showcase {
  max-width: 800px;
  margin: 0 auto;
}

.device-image {
  margin-bottom: 40px;
}

.device-placeholder {
  background: #2a2b31;
  border-radius: 12px;
  padding: 60px 40px;
  text-align: center;
  border: 2px dashed #3498db;
  max-width: 300px;
  margin: 0 auto;
}

.device-icon {
  font-size: 80px;
  color: #3498db;
}

.main-title {
  font-size: 36px;
  margin-bottom: 20px;
  color: #ffffff;
  font-weight: 600;
}

.main-description {
  font-size: 16px;
  line-height: 1.6;
  color: #b0b3b8;
  max-width: 600px;
  margin: 0 auto;
}

.process-section {
  background: #2a2b31;
  padding: 80px 0;
}

.process-header {
  text-align: center;
  margin-bottom: 60px;
}

.process-header h2 {
  font-size: 32px;
  margin-bottom: 15px;
  color: #ffffff;
  font-weight: 600;
}

.process-header p {
  color: #b0b3b8;
  font-size: 16px;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.step {
  text-align: center;
}

.step-icon {
  width: 60px;
  height: 60px;
  background: #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.step-number {
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;
}

.step h4 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #ffffff;
}

.step p {
  color: #b0b3b8;
  font-size: 14px;
  line-height: 1.5;
}

.product-gallery {
  padding: 80px 0;
}

.gallery-header {
  text-align: center;
  margin-bottom: 60px;
}

.gallery-header h2 {
  font-size: 32px;
  margin-bottom: 15px;
  color: #ffffff;
  font-weight: 600;
}

.gallery-header p {
  color: #b0b3b8;
  font-size: 16px;
}

.product-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-row {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.product-item {
  flex: 0 0 auto;
  width: 120px;
  height: 120px;
}

.product-item.large {
  width: 200px;
  height: 200px;
}

.product-placeholder {
  background: #2a2b31;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b0b3b8;
  font-size: 12px;
  text-align: center;
  border: 1px solid #3a3b41;
  transition: all 0.3s ease;
}

.product-placeholder:hover {
  background: #3a3b41;
  border-color: #3498db;
  color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }

  .main-title {
    font-size: 28px;
  }

  .process-steps {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .product-item {
    width: 100px;
    height: 100px;
  }

  .product-item.large {
    width: 160px;
    height: 160px;
  }

  .product-row {
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .power-distribution {
    padding-top: 140px; /* 移动端增加顶部间距 */
  }

  .main-title {
    font-size: 24px;
  }

  .process-steps {
    grid-template-columns: 1fr;
  }

  .product-item {
    width: 80px;
    height: 80px;
  }

  .product-item.large {
    width: 120px;
    height: 120px;
  }

  .product-placeholder {
    font-size: 10px;
  }
}
</style>

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 15px;
  scroll-behavior: smooth;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

#app {
  min-height: 100vh;
  width: 100%;
}

/* Add top margin to account for fixed navbar */
.app-nav+* {
  margin-top: 80px;
}

/* Link Styles */
a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

a:hover {
  color: #3498db;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-secondary:hover {
  background-color: #3498db;
  color: white;
}

/* Container */
.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Main Content Container - Global 1200px max-width
 * Use this class for main content areas across all pages
 * Provides consistent 1200px max-width with automatic centering
 * Responsive: uses padding on smaller screens instead of fixed width
 */
.content-container {
  max-width: 1980px;
  margin: 0 auto;
  padding: 0 calc((100vw - 1200px) / 2);
}

/* Responsive container adjustments */
@media (max-width: 1200px) {
  .container {
    max-width: 1200px;
    padding: 0 15px;
  }

  .content-container {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 100%;
    padding: 0 15px;
  }

  .content-container {
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  .content-container {
    padding: 0 15px;
  }
}

/* Responsive Images */
img {
  max-width: 100%;
  height: auto;
}

/* Form Elements */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 2rem;
}

/* Global responsive typography */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
}

/* Improved button responsiveness */
@media (max-width: 768px) {
  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

/* Better form element sizing on mobile */
@media (max-width: 768px) {

  input,
  textarea,
  select {
    font-size: 16px;
    /* Prevents zoom on iOS */
  }
}
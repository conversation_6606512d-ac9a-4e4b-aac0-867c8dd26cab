<template>
  <div class="industry-solution">
    <section class="hero">
      <div class="content-container">
        <h1 class="page-title">行业解决方案</h1>
        <p class="page-subtitle">为不同行业提供定制化的能源管理解决方案</p>
      </div>
    </section>

    <section class="solutions">
      <div class="content-container">
        <div class="solutions-grid">
          <div class="solution-card">
            <div class="solution-icon">
              <img src="/images/icon01.svg" alt="制造业" />
            </div>
            <div class="solution-content">
              <h3>制造业解决方案</h3>
              <p>
                为制造企业提供全面的能源监控和管理系统，优化生产流程，降低能耗成本。
              </p>
              <ul class="solution-features">
                <li>生产线能耗监控</li>
                <li>设备效率分析</li>
                <li>能源成本优化</li>
                <li>碳排放管理</li>
              </ul>
              <button class="solution-btn">了解更多</button>
            </div>
          </div>

          <div class="solution-card">
            <div class="solution-icon">
              <img src="/images/icon02.svg" alt="商业建筑" />
            </div>
            <div class="solution-content">
              <h3>商业建筑解决方案</h3>
              <p>
                智能楼宇能源管理系统，实现建筑能耗的精细化管理和智能化控制。
              </p>
              <ul class="solution-features">
                <li>楼宇自动化控制</li>
                <li>照明系统管理</li>
                <li>空调系统优化</li>
                <li>能耗数据分析</li>
              </ul>
              <button class="solution-btn">了解更多</button>
            </div>
          </div>

          <div class="solution-card">
            <div class="solution-icon">
              <img src="/images/icon03.svg" alt="医疗机构" />
            </div>
            <div class="solution-content">
              <h3>医疗机构解决方案</h3>
              <p>
                为医院和医疗机构提供可靠的电力保障和能源管理，确保医疗设备正常运行。
              </p>
              <ul class="solution-features">
                <li>不间断电源管理</li>
                <li>医疗设备监控</li>
                <li>应急电源切换</li>
                <li>能耗成本控制</li>
              </ul>
              <button class="solution-btn">了解更多</button>
            </div>
          </div>

          <div class="solution-card">
            <div class="solution-icon">
              <img src="/images/icon04.svg" alt="数据中心" />
            </div>
            <div class="solution-content">
              <h3>数据中心解决方案</h3>
              <p>
                专业的数据中心能源管理系统，确保服务器稳定运行，优化制冷和供电效率。
              </p>
              <ul class="solution-features">
                <li>机房环境监控</li>
                <li>制冷系统优化</li>
                <li>UPS系统管理</li>
                <li>PUE效率提升</li>
              </ul>
              <button class="solution-btn">了解更多</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="case-studies">
      <div class="content-container">
        <h2 class="section-title">成功案例</h2>
        <div class="cases-grid">
          <div class="case-card">
            <div class="case-image">
              <img src="/images/容器@1x.png" alt="案例1" />
            </div>
            <div class="case-content">
              <h3>某大型制造企业能源管理项目</h3>
              <p>
                通过实施我们的能源管理系统，该企业年节能率达到15%，投资回收期仅为2年。
              </p>
            </div>
          </div>
          <div class="case-card">
            <div class="case-image">
              <img src="/images/容器@1x-2.png" alt="案例2" />
            </div>
            <div class="case-content">
              <h3>智慧园区综合能源管理</h3>
              <p>
                为某科技园区提供综合能源管理解决方案，实现了园区能源的统一调度和优化配置。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Industry Solution logic
</script>

<style scoped>
.industry-solution {
  margin-top: 80px;
  min-height: 100vh;
}

.hero {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.page-subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
}

.solutions {
  padding: 80px 0;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.solution-card {
  background: white;
  border-radius: 10px;
  padding: 40px 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  text-align: center;
}

.solution-card:hover {
  transform: translateY(-5px);
}

.solution-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.solution-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.solution-content h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #2c3e50;
}

.solution-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.solution-features {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
  text-align: left;
}

.solution-features li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.solution-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #27ae60;
  font-weight: bold;
}

.solution-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.solution-btn:hover {
  background: #2980b9;
}

.case-studies {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  color: #2c3e50;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.case-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.case-image {
  height: 200px;
  overflow: hidden;
}

.case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.case-content {
  padding: 30px;
}

.case-content h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #2c3e50;
}

.case-content p {
  color: #666;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .cases-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<template>
  <div class="building-control">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-bg-placeholder"></div>
      </div>
      <div class="hero-content">
        <div class="container">
          <div class="hero-text">
            <h1 class="hero-title">智能楼宇控制解决方案</h1>
            <p class="hero-subtitle">
              基于物联网、大数据、人工智能等先进技术，为楼宇提供全方位的智能化管理解决方案
            </p>
            <div class="hero-buttons">
              <button class="btn-primary">了解更多</button>
              <button class="btn-secondary">联系我们</button>
            </div>
          </div>
          <div class="hero-image">
            <div class="building-display">
              <div class="building-screen">
                <div class="building-interface">
                  <div class="interface-header">智能楼宇控制系统</div>
                  <div class="interface-content">
                    <div class="control-panel">
                      <div class="control-item">
                        <span class="control-label">照明系统</span>
                        <div class="control-status active"></div>
                      </div>
                      <div class="control-item">
                        <span class="control-label">空调系统</span>
                        <div class="control-status active"></div>
                      </div>
                      <div class="control-item">
                        <span class="control-label">安防系统</span>
                        <div class="control-status active"></div>
                      </div>
                      <div class="control-item">
                        <span class="control-label">消防系统</span>
                        <div class="control-status"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 3D Building Model Section -->
    <section class="model-section">
      <div class="container">
        <h2 class="section-title">因地制宜 分类指导</h2>
        <p class="section-subtitle">
          根据不同建筑类型和需求，提供定制化的智能楼宇控制解决方案
        </p>

        <!-- 3D Building Model Display -->
        <div class="model-display">
          <div class="building-3d">
            <!-- 3D建筑模型 -->
            <div class="building-structure">
              <div class="building-floors">
                <div class="floor floor-1"></div>
                <div class="floor floor-2"></div>
                <div class="floor floor-3"></div>
                <div class="floor floor-4"></div>
              </div>
              <div class="building-base"></div>
            </div>

            <!-- 功能标注点 -->
            <div class="annotation annotation-1">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>照明控制</span>
                <div class="annotation-detail">智能照明管理</div>
              </div>
            </div>

            <div class="annotation annotation-2">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>空调系统</span>
                <div class="annotation-detail">温度智能调节</div>
              </div>
            </div>

            <div class="annotation annotation-3">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>安防监控</span>
                <div class="annotation-detail">24小时安全监控</div>
              </div>
            </div>

            <div class="annotation annotation-4">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>消防系统</span>
                <div class="annotation-detail">火灾预警与控制</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">智能楼宇控制核心功能</h2>
        <p class="section-subtitle">
          数字化全面覆盖楼宇管理的各个环节，提升效率，降低成本
        </p>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">💡</div>
            <h3>智能照明控制</h3>
            <p>根据环境光线和人员活动自动调节照明亮度，节能环保</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🌡️</div>
            <h3>温度环境控制</h3>
            <p>智能空调系统，自动调节室内温度，提供舒适环境</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔒</div>
            <h3>安防监控系统</h3>
            <p>全方位安防监控，智能识别异常情况，保障楼宇安全</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🚨</div>
            <h3>消防安全管理</h3>
            <p>智能消防系统，实时监测火灾隐患，快速响应处理</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Section -->
    <section class="technology">
      <div class="container">
        <div class="tech-content">
          <div class="tech-text">
            <h2>
              智能楼宇管理系统，实现楼宇、设备和<br />用户智能化的优化整合
            </h2>
            <p>
              集成先进的物联网技术、智能算法和管理系统，实现楼宇的全面智能化管理
            </p>
            <div class="tech-features">
              <div class="tech-feature">
                <h4>集中控制</h4>
                <p>统一的楼宇管理平台，集中控制各个子系统</p>
              </div>
              <div class="tech-feature">
                <h4>智能调节</h4>
                <p>基于AI算法的智能调节，提高能效和舒适度</p>
              </div>
              <div class="tech-feature">
                <h4>远程监控</h4>
                <p>支持远程监控和管理，随时掌握楼宇状态</p>
              </div>
            </div>
          </div>
          <div class="tech-image">
            <div class="control-system-display">
              <div class="system-screen">
                <div class="system-interface">
                  <div class="system-header">楼宇控制管理系统</div>
                  <div class="system-dashboard">
                    <div class="dashboard-section">
                      <div class="section-title">系统状态</div>
                      <div class="status-grid">
                        <div class="status-item">
                          <span class="status-label">照明</span>
                          <div class="status-indicator active"></div>
                        </div>
                        <div class="status-item">
                          <span class="status-label">空调</span>
                          <div class="status-indicator active"></div>
                        </div>
                        <div class="status-item">
                          <span class="status-label">安防</span>
                          <div class="status-indicator active"></div>
                        </div>
                        <div class="status-item">
                          <span class="status-label">消防</span>
                          <div class="status-indicator"></div>
                        </div>
                      </div>
                    </div>
                    <div class="dashboard-section">
                      <div class="section-title">能耗监控</div>
                      <div class="energy-chart">
                        <div class="chart-bar" style="height: 60%"></div>
                        <div class="chart-bar" style="height: 80%"></div>
                        <div class="chart-bar" style="height: 45%"></div>
                        <div class="chart-bar" style="height: 70%"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Cases Section -->
    <section class="cases">
      <div class="container">
        <h2 class="section-title">智能楼宇控制应用场景</h2>
        <p class="section-subtitle">
          为不同类型的建筑提供专业的智能化控制解决方案
        </p>
        <div class="cases-grid">
          <div class="case-item">
            <div class="case-image">
              <div class="case-placeholder">办公大楼</div>
            </div>
            <h3>办公大楼</h3>
            <p>为现代办公楼提供全面的智能化管理，提升办公环境舒适度</p>
          </div>
          <div class="case-item">
            <div class="case-image">
              <div class="case-placeholder">商业综合体</div>
            </div>
            <h3>商业综合体</h3>
            <p>大型商业建筑的智能化管理，优化能耗和运营效率</p>
          </div>
          <div class="case-item">
            <div class="case-image">
              <div class="case-placeholder">住宅小区</div>
            </div>
            <h3>住宅小区</h3>
            <p>智能住宅管理系统，提升居住体验和安全性</p>
          </div>
          <div class="case-item">
            <div class="case-image">
              <div class="case-placeholder">工业园区</div>
            </div>
            <h3>工业园区</h3>
            <p>工业建筑的智能化控制，保障生产安全和效率</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 楼宇控制解决方案页面逻辑
</script>

<style scoped>
.building-control {
  padding-top: 130px;
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1b21 0%, #2c3e50 100%);
  opacity: 0.8;
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 80px;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  color: #ffffff;
}

.hero-subtitle {
  font-size: 18px;
  color: #b0b3b8;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-primary {
  background: #3498db;
  color: #ffffff;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-secondary:hover {
  background: #3498db;
  color: #ffffff;
  transform: translateY(-2px);
}

.hero-image {
  flex: 0 0 500px;
}

/* Building Display Styling */
.building-display {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.building-screen {
  width: 100%;
  height: 350px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 3px solid #3498db;
  overflow: hidden;
  position: relative;
}

.building-interface {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.interface-header {
  color: #3498db;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 1px solid #3498db;
  padding-bottom: 10px;
}

.interface-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.control-label {
  color: #ecf0f1;
  font-size: 14px;
}

.control-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #7f8c8d;
  transition: all 0.3s;
}

.control-status.active {
  background: #27ae60;
  box-shadow: 0 0 10px #27ae60;
}

/* 3D Model Section */
.model-section {
  background: #2a2b31;
  padding: 120px 0;
}

.section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 20px;
  color: #ffffff;
}

.section-subtitle {
  font-size: 18px;
  text-align: center;
  color: #b0b3b8;
  margin-bottom: 80px;
}

.model-display {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 80px;
}

.building-3d {
  position: relative;
  width: 600px;
  height: 400px;
  background: #3a3b41;
  border-radius: 12px;
  border: 2px solid #3498db;
  overflow: hidden;
  perspective: 1000px;
}

.building-structure {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  position: relative;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.building-floors {
  display: flex;
  flex-direction: column-reverse;
  gap: 5px;
  transform: rotateX(15deg) rotateY(-15deg);
}

.floor {
  width: 200px;
  height: 40px;
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px;
  border: 1px solid #5dade2;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.floor::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #f1c40f;
  border-radius: 50%;
  box-shadow: 0 0 8px #f1c40f;
}

.floor::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 2px;
  background: #ecf0f1;
  border-radius: 1px;
}

.building-base {
  width: 220px;
  height: 20px;
  background: #34495e;
  border-radius: 0 0 10px 10px;
  margin-top: 10px;
  transform: rotateX(15deg) rotateY(-15deg);
}

/* Annotation Styles */
.annotation {
  position: absolute;
  z-index: 10;
}

.annotation-1 {
  top: 15%;
  left: 15%;
}

.annotation-2 {
  top: 15%;
  right: 15%;
}

.annotation-3 {
  bottom: 25%;
  left: 15%;
}

.annotation-4 {
  bottom: 25%;
  right: 15%;
}

.annotation-dot {
  width: 12px;
  height: 12px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.annotation-label {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
}

.annotation:hover .annotation-label {
  opacity: 1;
}

.annotation-detail {
  font-size: 10px;
  color: #b0b3b8;
  margin-top: 2px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Features Section */
.features {
  background: #f8f9fa;
  padding: 120px 0;
}

.features .section-title {
  color: #2c3e50;
}

.features .section-subtitle {
  color: #7f8c8d;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
}

/* Technology Section */
.technology {
  background: #2a2b31;
  padding: 120px 0;
}

.tech-content {
  display: flex;
  align-items: center;
  gap: 80px;
}

.tech-text {
  flex: 1;
}

.tech-text h2 {
  font-size: 36px;
  margin-bottom: 20px;
  color: #ffffff;
  line-height: 1.4;
}

.tech-text > p {
  font-size: 18px;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.tech-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-feature h4 {
  font-size: 20px;
  color: #3498db;
  margin-bottom: 8px;
}

.tech-feature p {
  color: #b0b3b8;
}

.tech-image {
  flex: 1;
}

/* Control System Display Styling */
.control-system-display {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.system-screen {
  width: 100%;
  height: 400px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 3px solid #3498db;
  overflow: hidden;
  position: relative;
}

.system-interface {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.system-header {
  color: #3498db;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 1px solid #3498db;
  padding-bottom: 10px;
}

.system-dashboard {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dashboard-section {
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.dashboard-section .section-title {
  color: #3498db;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: left;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.status-label {
  color: #ecf0f1;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #7f8c8d;
  transition: all 0.3s;
}

.status-indicator.active {
  background: #27ae60;
  box-shadow: 0 0 8px #27ae60;
}

.energy-chart {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 60px;
  padding: 10px 0;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, #3498db, #5dade2);
  border-radius: 2px;
  min-height: 20px;
  transition: all 0.3s;
}

.chart-bar:hover {
  background: linear-gradient(to top, #2980b9, #3498db);
}

/* Cases Section */
.cases {
  background: #f8f9fa;
  padding: 120px 0;
}

.cases .section-title {
  color: #2c3e50;
}

.cases .section-subtitle {
  color: #7f8c8d;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.case-item {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.case-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.case-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.case-item h3 {
  font-size: 20px;
  margin: 20px 20px 10px;
  color: #2c3e50;
}

.case-item p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 20px 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 40px;
  }

  .hero-title {
    font-size: 32px;
  }

  .tech-content {
    flex-direction: column;
    gap: 40px;
  }

  .building-3d {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  .features-grid,
  .cases-grid {
    grid-template-columns: 1fr;
  }
}
</style>

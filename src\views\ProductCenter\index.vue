<template>
  <div class="product-center">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <img
          src="/images/<EMAIL>"
          alt="产品中心背景"
          class="hero-bg-image"
        />
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            设备全生命周期管理平台，<br />
            让电力资产专业运行
          </h1>
          <p class="hero-subtitle">
            基于物联网、大数据、人工智能等先进技术，为电力行业提供全方位的设备管理解决方案
          </p>
        </div>
        <div class="hero-dashboard">
          <img
            src="/images/<EMAIL>"
            alt="管理平台预览"
            class="dashboard-image"
          />
        </div>
      </div>
    </section>

    <!-- Standards Section -->
    <section class="standards">
      <div class="content-container">
        <h2 class="standards-title">
          We always follow the standard work process
        </h2>
        <p class="standards-subtitle">
          严格遵循标准化工作流程，确保产品质量与服务水准
        </p>

        <div class="standards-grid">
          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">01</div>
            </div>
            <h3>需求分析</h3>
            <p>深入了解客户需求，制定个性化解决方案</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">02</div>
            </div>
            <h3>方案设计</h3>
            <p>基于行业最佳实践，设计最优技术架构</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">03</div>
            </div>
            <h3>产品开发</h3>
            <p>采用敏捷开发模式，确保产品快速迭代</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">04</div>
            </div>
            <h3>质量保证</h3>
            <p>严格的测试流程，保障产品稳定可靠</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Product Overview Section -->
    <section class="product-overview">
      <div class="content-container">
        <h2 class="section-title">
          一体化平台解决方案，全面覆盖电力设备管理需求
        </h2>
        <p class="section-subtitle">
          从设备监控到资产管理，从数据分析到智能运维，提供完整的产品生态
        </p>

        <div class="product-ecosystem">
          <img
            src="/images/容器@2x.png"
            alt="产品生态系统"
            class="ecosystem-image"
          />
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products">
      <div class="content-container">
        <h2 class="products-title">产品中心</h2>

        <!-- Asset Management Product - Featured -->
        <div class="featured-product">
          <div class="product-content">
            <div class="product-info">
              <h3 class="product-title">资产管理系统（APM）</h3>
              <p class="product-description">
                基于物联网技术的智能资产管理平台，实现设备全生命周期管理。
                通过实时监控、预测性维护、智能分析等功能，帮助企业提高设备利用率，
                降低运维成本，确保设备安全稳定运行。
              </p>
              <div class="product-features">
                <div class="feature-item">
                  <div class="feature-icon">📊</div>
                  <span>实时监控</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">🔧</div>
                  <span>预测维护</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">📈</div>
                  <span>智能分析</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">🛡️</div>
                  <span>安全保障</span>
                </div>
              </div>
              <button class="product-btn primary">了解详情</button>
            </div>
            <div class="product-visual">
              <img
                src="/images/<EMAIL>"
                alt="资产管理系统界面"
                class="product-image"
              />
            </div>
          </div>
        </div>

        <!-- Other Products Grid -->
        <div class="products-grid">
          <div class="product-card">
            <div class="product-header">
              <img
                src="/images/icon01.svg"
                alt="电力监控"
                class="product-icon"
              />
              <h4>电力监控系统</h4>
            </div>
            <p class="product-desc">
              实时监控电力系统运行状态，提供全面的电力数据分析和故障预警功能。
            </p>
            <div class="product-tags">
              <span class="tag">实时监控</span>
              <span class="tag">数据分析</span>
              <span class="tag">故障预警</span>
            </div>
            <button class="product-btn secondary">了解更多</button>
          </div>

          <div class="product-card">
            <div class="product-header">
              <img
                src="/images/icon02.svg"
                alt="能源管理"
                class="product-icon"
              />
              <h4>能源管理系统</h4>
            </div>
            <p class="product-desc">
              智能化能源管理平台，优化能源配置，降低运营成本，支持多种能源类型统一管理。
            </p>
            <div class="product-tags">
              <span class="tag">智能管理</span>
              <span class="tag">成本优化</span>
              <span class="tag">统一平台</span>
            </div>
            <button class="product-btn secondary">了解更多</button>
          </div>

          <div class="product-card">
            <div class="product-header">
              <img
                src="/images/icon03.svg"
                alt="数据分析"
                class="product-icon"
              />
              <h4>数据分析平台</h4>
            </div>
            <p class="product-desc">
              深度数据分析平台，为决策提供科学依据，支持多维度数据挖掘和可视化展示。
            </p>
            <div class="product-tags">
              <span class="tag">深度分析</span>
              <span class="tag">数据挖掘</span>
              <span class="tag">可视化</span>
            </div>
            <button class="product-btn secondary">了解更多</button>
          </div>
        </div>
      </div>
    </section>

    <!-- 3D Model Section -->
    <section class="model-section">
      <div class="content-container">
        <div class="model-content">
          <div class="model-info">
            <h3 class="model-title">三维模型化设备管理</h3>
            <p class="model-description">
              采用先进的三维建模技术，为设备管理提供直观的可视化界面。
              通过3D模型展示设备状态、位置信息和运行参数，让设备管理更加智能化、可视化。
            </p>
            <div class="model-features">
              <div class="model-feature">
                <span class="feature-number">01</span>
                <div class="feature-content">
                  <h4>3D可视化</h4>
                  <p>真实还原设备外观和内部结构</p>
                </div>
              </div>
              <div class="model-feature">
                <span class="feature-number">02</span>
                <div class="feature-content">
                  <h4>实时状态</h4>
                  <p>动态显示设备运行状态和参数</p>
                </div>
              </div>
              <div class="model-feature">
                <span class="feature-number">03</span>
                <div class="feature-content">
                  <h4>交互操作</h4>
                  <p>支持多角度查看和交互式操作</p>
                </div>
              </div>
            </div>
          </div>
          <div class="model-visual">
            <img
              src="/images/<EMAIL>"
              alt="3D模型预览"
              class="model-image"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Customer Service Section -->
    <section class="customer-service">
      <div class="content-container">
        <h2 class="service-title">Customers we serve</h2>
        <div class="service-cards">
          <div class="service-card">
            <div class="service-icon">
              <img src="/images/icon04.svg" alt="服务图标" />
            </div>
            <h4>电力公司</h4>
            <p>
              为电力公司提供全面的设备管理和监控解决方案，确保电网安全稳定运行。
            </p>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <img src="/images/icon04.svg" alt="服务图标" />
            </div>
            <h4>工业企业</h4>
            <p>帮助工业企业优化设备管理流程，提高生产效率，降低运营成本。</p>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <img src="/images/icon04.svg" alt="服务图标" />
            </div>
            <h4>数据中心</h4>
            <p>为数据中心提供智能化的设备监控和能源管理解决方案。</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Product Center logic
</script>

<style scoped>
.product-center {
  padding-top: 130px; /* 为主导航+二级导航预留空间 */
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  background: #1e1f25;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  padding: 0 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.hero-text {
  color: #ffffff;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  color: #ffffff;
}

.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 40px;
}

.hero-dashboard {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dashboard-image {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Standards Section */
.standards {
  padding: 100px 0;
  background: #1e1f25;
  color: #ffffff;
}

.standards-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
}

.standards-subtitle {
  text-align: center;
  font-size: 1.1rem;
  opacity: 0.8;
  margin-bottom: 60px;
}

.standards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.standard-item {
  text-align: center;
}

.standard-icon {
  margin-bottom: 20px;
}

.icon-circle {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 auto;
}

.standard-item h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #ffffff;
}

.standard-item p {
  color: #cccccc;
  line-height: 1.6;
}

/* Product Overview Section */
.product-overview {
  padding: 100px 0;
  background: #1e1f25;
  text-align: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
}

.section-subtitle {
  font-size: 1.1rem;
  opacity: 0.8;
  margin-bottom: 60px;
  color: #cccccc;
}

.product-ecosystem {
  max-width: 800px;
  margin: 0 auto;
}

.ecosystem-image {
  width: 100%;
  height: auto;
  border-radius: 10px;
}

/* Products Section */
.products {
  padding: 100px 0;
  background: #1e1f25;
}

.products-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 80px;
  color: #ffffff;
}

/* Featured Product */
.featured-product {
  margin-bottom: 80px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.featured-product .product-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  padding: 60px;
}

.product-info {
  color: #ffffff;
}

.product-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
}

.product-description {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 40px;
  opacity: 0.9;
}

.product-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.feature-icon {
  font-size: 1.5rem;
}

.feature-item span {
  font-size: 1rem;
  font-weight: 500;
}

.product-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Product Buttons */
.product-btn {
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.product-btn.primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: #ffffff;
}

.product-btn.primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
}

.product-btn.secondary {
  background: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.product-btn.secondary:hover {
  background: #3498db;
  color: #ffffff;
  transform: translateY(-2px);
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.product-card {
  background: #2c3e50;
  border-radius: 15px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #34495e;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: #3498db;
}

.product-header {
  margin-bottom: 20px;
}

.product-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 15px;
  display: block;
}

.product-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
}

.product-desc {
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 25px;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-bottom: 25px;
}

.tag {
  background: rgba(52, 152, 219, 0.2);
  color: #3498db;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 3D Model Section */
.model-section {
  padding: 100px 0;
  background: #1e1f25;
}

.model-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.model-info {
  color: #ffffff;
}

.model-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 25px;
  color: #ffffff;
}

.model-description {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 40px;
  opacity: 0.9;
  color: #cccccc;
}

.model-features {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.model-feature {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.feature-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #ffffff;
  flex-shrink: 0;
}

.feature-content h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #ffffff;
}

.feature-content p {
  color: #cccccc;
  line-height: 1.6;
}

.model-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.model-image {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Customer Service Section */
.customer-service {
  padding: 100px 0;
  background: #1e1f25;
}

.service-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 80px;
  color: #ffffff;
}

.service-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.service-card {
  background: #2c3e50;
  border-radius: 15px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #34495e;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: #3498db;
}

.service-icon {
  margin-bottom: 25px;
}

.service-icon img {
  width: 60px;
  height: 60px;
}

.service-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffffff;
}

.service-card p {
  color: #cccccc;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 3rem;
  }

  .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .featured-product .product-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .model-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .service-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .standards-grid {
    grid-template-columns: 1fr;
  }

  .section-title,
  .standards-title,
  .products-title,
  .model-title,
  .service-title {
    font-size: 2rem;
  }

  .product-title {
    font-size: 2rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .service-cards {
    grid-template-columns: 1fr;
  }

  .product-features {
    grid-template-columns: 1fr;
  }

  .featured-product .product-content {
    padding: 40px 30px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title,
  .standards-title,
  .products-title,
  .model-title,
  .service-title {
    font-size: 1.8rem;
  }

  .featured-product .product-content {
    padding: 30px 20px;
  }

  .product-card,
  .service-card {
    padding: 30px 20px;
  }
}
</style>

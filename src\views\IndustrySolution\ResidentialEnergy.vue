<template>
  <div class="residential-energy">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-bg-placeholder"></div>
      </div>
      <div class="hero-content">
        <div class="container">
          <div class="hero-text">
            <h1 class="hero-title">
              Realtor Home<br />
              Monitoring system
            </h1>
            <p class="hero-subtitle">
              智能家居监控系统为住宅提供全方位的能源管理和安全监控，<br />
              通过先进的物联网技术实现智能化的家居生活体验，<br />
              让您的家更安全、更节能、更舒适。
            </p>
            <div class="hero-buttons">
              <button class="btn-primary">了解更多</button>
              <button class="btn-secondary">联系我们</button>
            </div>
          </div>
          <div class="hero-image">
            <div class="phone-mockup">
              <div class="phone-screen">
                <div class="phone-interface">
                  <div class="interface-header">
                    <div class="status-bar">
                      <span class="time">9:41</span>
                      <div class="signal-icons">
                        <div class="signal-dot"></div>
                        <div class="signal-dot"></div>
                        <div class="signal-dot"></div>
                      </div>
                    </div>
                  </div>
                  <div class="interface-content">
                    <div class="home-control-grid">
                      <div class="control-card">
                        <div class="card-icon">💡</div>
                        <span class="card-label">照明</span>
                      </div>
                      <div class="control-card">
                        <div class="card-icon">🌡️</div>
                        <span class="card-label">温度</span>
                      </div>
                      <div class="control-card">
                        <div class="card-icon">🔒</div>
                        <span class="card-label">安防</span>
                      </div>
                      <div class="control-card">
                        <div class="card-icon">⚡</div>
                        <span class="card-label">能源</span>
                      </div>
                    </div>
                    <div class="energy-chart">
                      <div class="chart-title">今日能耗</div>
                      <div class="chart-bars">
                        <div class="chart-bar" style="height: 60%"></div>
                        <div class="chart-bar" style="height: 80%"></div>
                        <div class="chart-bar" style="height: 45%"></div>
                        <div class="chart-bar" style="height: 70%"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Floating Elements -->
            <div class="floating-element element-1">
              <div class="element-icon">🏠</div>
            </div>
            <div class="floating-element element-2">
              <div class="element-icon">📱</div>
            </div>
            <div class="floating-element element-3">
              <div class="element-icon">⚡</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 3D Model Section -->
    <section class="model-section">
      <div class="container">
        <h2 class="section-title">
          住宅能源管理系统，一个完整平台的功能方案，专<br />门针对智能家居、能源监控和设备自动化的解决方案
        </h2>

        <!-- 3D House Model Display -->
        <div class="model-display">
          <div class="house-3d">
            <!-- 3D房屋模型 -->
            <div class="house-structure">
              <div class="house-roof"></div>
              <div class="house-walls">
                <div class="wall front-wall">
                  <div class="window"></div>
                  <div class="door"></div>
                </div>
                <div class="wall side-wall"></div>
              </div>
              <div class="house-base"></div>
            </div>

            <!-- 功能标注点 -->
            <div class="annotation annotation-1">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>智能照明控制</span>
                <div class="annotation-detail">自动调节室内照明</div>
              </div>
            </div>

            <div class="annotation annotation-2">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>能源监控</span>
                <div class="annotation-detail">实时监控能源消耗</div>
              </div>
            </div>

            <div class="annotation annotation-3">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>安全监控</span>
                <div class="annotation-detail">24小时安全防护</div>
              </div>
            </div>

            <div class="annotation annotation-4">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>环境控制</span>
                <div class="annotation-detail">智能温湿度调节</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <div class="features-content">
          <div class="features-text">
            <h2>transforming homes into smart<br />energy-efficient spaces</h2>
            <div class="features-list">
              <div class="feature-item">
                <div class="feature-icon">🏠</div>
                <div class="feature-content">
                  <h4>智能家居集成</h4>
                  <p>全面集成各类智能设备，实现统一管理和控制</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="feature-icon">📊</div>
                <div class="feature-content">
                  <h4>能源数据分析</h4>
                  <p>详细的能源消耗分析，帮助优化用电习惯</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🔐</div>
                <div class="feature-content">
                  <h4>安全防护系统</h4>
                  <p>多层次安全防护，保障家庭安全</p>
                </div>
              </div>
            </div>
          </div>
          <div class="features-image">
            <div class="feature-visual">
              <div class="visual-circle">
                <div class="center-logo">CGC</div>
                <div class="orbit-item orbit-1">
                  <div class="orbit-icon">🏠</div>
                </div>
                <div class="orbit-item orbit-2">
                  <div class="orbit-icon">📱</div>
                </div>
                <div class="orbit-item orbit-3">
                  <div class="orbit-icon">⚡</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Mobile App Section -->
    <section class="mobile-app">
      <div class="container">
        <h2 class="section-title">家庭能源管理中心</h2>
        <p class="section-subtitle">
          通过移动应用程序，随时随地管理您的智能家居
        </p>

        <div class="app-showcase">
          <div class="app-phones">
            <div class="phone-device phone-1">
              <div class="phone-screen-content">
                <div class="app-interface">
                  <div class="app-header">能源监控</div>
                  <div class="energy-overview">
                    <div class="energy-card">
                      <span class="energy-label">今日用电</span>
                      <span class="energy-value">24.5 kWh</span>
                    </div>
                    <div class="energy-card">
                      <span class="energy-label">本月用电</span>
                      <span class="energy-value">680 kWh</span>
                    </div>
                  </div>
                  <div class="device-list">
                    <div class="device-item">
                      <span class="device-name">客厅照明</span>
                      <div class="device-status on"></div>
                    </div>
                    <div class="device-item">
                      <span class="device-name">空调系统</span>
                      <div class="device-status on"></div>
                    </div>
                    <div class="device-item">
                      <span class="device-name">热水器</span>
                      <div class="device-status off"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="phone-device phone-2">
              <div class="phone-screen-content">
                <div class="app-interface">
                  <div class="app-header">设备控制</div>
                  <div class="control-grid">
                    <div class="control-item">
                      <div class="control-icon">💡</div>
                      <span class="control-label">照明</span>
                      <div class="control-toggle active"></div>
                    </div>
                    <div class="control-item">
                      <div class="control-icon">🌡️</div>
                      <span class="control-label">空调</span>
                      <div class="control-toggle active"></div>
                    </div>
                    <div class="control-item">
                      <div class="control-icon">🔒</div>
                      <span class="control-label">门锁</span>
                      <div class="control-toggle"></div>
                    </div>
                    <div class="control-item">
                      <div class="control-icon">📹</div>
                      <span class="control-label">监控</span>
                      <div class="control-toggle active"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="app-features">
            <div class="app-feature">
              <div class="feature-dot green"></div>
              <div class="feature-text">
                <h4>实时监控</h4>
                <p>24小时实时监控家庭能源使用情况</p>
              </div>
            </div>
            <div class="app-feature">
              <div class="feature-dot blue"></div>
              <div class="feature-text">
                <h4>远程控制</h4>
                <p>随时随地远程控制家中的智能设备</p>
              </div>
            </div>
            <div class="app-feature">
              <div class="feature-dot orange"></div>
              <div class="feature-text">
                <h4>智能分析</h4>
                <p>AI智能分析，提供节能建议和优化方案</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 住宅能源解决方案页面逻辑
</script>

<style scoped>
.residential-energy {
  padding-top: 130px;
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-bg-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e1f25 0%, #2a2b31 50%, #1e1f25 100%);
  position: relative;
}

.hero-bg-placeholder::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 40%,
      rgba(52, 152, 219, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 60%,
      rgba(231, 76, 60, 0.1) 0%,
      transparent 50%
    );
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 80px;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #ffffff 0%, #b0b3b8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 16px;
  line-height: 1.8;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: #ffffff;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #ffffff;
  border: 2px solid #3498db;
  padding: 13px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background: #3498db;
  color: #ffffff;
  transform: translateY(-2px);
}

.hero-image {
  flex: 0 0 500px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Phone Mockup */
.phone-mockup {
  position: relative;
  width: 280px;
  height: 560px;
  background: #000000;
  border-radius: 30px;
  padding: 8px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 22px;
  overflow: hidden;
  position: relative;
}

.phone-interface {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.interface-header {
  margin-bottom: 30px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

.signal-icons {
  display: flex;
  gap: 4px;
}

.signal-dot {
  width: 8px;
  height: 8px;
  background: #27ae60;
  border-radius: 50%;
}

.interface-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.home-control-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.control-card {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s;
}

.control-card:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: translateY(-2px);
}

.card-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.card-label {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
}

.energy-chart {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
}

.chart-title {
  color: #b0b3b8;
  font-size: 14px;
  margin-bottom: 15px;
}

.chart-bars {
  display: flex;
  gap: 8px;
  align-items: end;
  height: 60px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  animation: chartGrow 2s ease-out;
}

@keyframes chartGrow {
  from {
    height: 0;
  }
  to {
    height: var(--height);
  }
}

/* Floating Elements */
.floating-element {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(52, 152, 219, 0.1);
  border: 2px solid #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: float 3s ease-in-out infinite;
}

.element-1 {
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.element-2 {
  top: 60%;
  right: -15%;
  animation-delay: 1s;
}

.element-3 {
  bottom: 20%;
  left: -5%;
  animation-delay: 2s;
}

.element-icon {
  font-size: 24px;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Model Section */
.model-section {
  background: #f8f9fa;
  padding: 120px 0;
  color: #2c3e50;
}

.model-section .container {
  flex-direction: column;
  gap: 60px;
}

.section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 80px;
  color: #2c3e50;
  line-height: 1.4;
}

.model-display {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}

/* 3D House Model */
.house-3d {
  position: relative;
  width: 600px;
  height: 400px;
  perspective: 1000px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.house-structure {
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(15deg) rotateY(-20deg);
  animation: houseRotate 20s infinite linear;
}

@keyframes houseRotate {
  0% {
    transform: rotateX(15deg) rotateY(-20deg);
  }
  25% {
    transform: rotateX(15deg) rotateY(-10deg);
  }
  50% {
    transform: rotateX(15deg) rotateY(0deg);
  }
  75% {
    transform: rotateX(15deg) rotateY(-10deg);
  }
  100% {
    transform: rotateX(15deg) rotateY(-20deg);
  }
}

.house-roof {
  width: 200px;
  height: 150px;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  position: relative;
  transform: translateZ(50px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.house-walls {
  position: relative;
  width: 200px;
  height: 150px;
  transform-style: preserve-3d;
}

.wall {
  position: absolute;
  width: 200px;
  height: 150px;
}

.front-wall {
  background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
  transform: translateZ(50px);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20px;
}

.side-wall {
  background: linear-gradient(135deg, #d5dbdb 0%, #a6acaf 100%);
  transform: rotateY(90deg) translateZ(50px);
  width: 100px;
}

.window {
  width: 40px;
  height: 50px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px;
  border: 2px solid #34495e;
  position: relative;
}

.window::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #34495e;
}

.window::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: #34495e;
}

.door {
  width: 30px;
  height: 80px;
  background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
  border-radius: 4px;
  border: 2px solid #34495e;
  position: relative;
}

.door::before {
  content: "";
  position: absolute;
  top: 40%;
  right: 5px;
  width: 4px;
  height: 4px;
  background: #f1c40f;
  border-radius: 50%;
}

.house-base {
  width: 220px;
  height: 20px;
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
  border-radius: 10px;
  transform: translateZ(-10px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Annotation Styles */
.annotation {
  position: absolute;
  z-index: 10;
}

.annotation-1 {
  top: 15%;
  left: 15%;
}

.annotation-2 {
  top: 15%;
  right: 15%;
}

.annotation-3 {
  bottom: 25%;
  left: 15%;
}

.annotation-4 {
  bottom: 25%;
  right: 15%;
}

.annotation-dot {
  width: 12px;
  height: 12px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.annotation-label {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.annotation:hover .annotation-label {
  opacity: 1;
}

.annotation-detail {
  font-size: 10px;
  color: #b0b3b8;
  margin-top: 4px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Features Section */
.features {
  background: #2a2b31;
  padding: 120px 0;
}

.features-content {
  display: flex;
  align-items: center;
  gap: 80px;
}

.features-text {
  flex: 1;
}

.features-text h2 {
  font-size: 36px;
  margin-bottom: 40px;
  color: #ffffff;
  line-height: 1.4;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.feature-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.feature-content h4 {
  font-size: 20px;
  color: #3498db;
  margin-bottom: 8px;
}

.feature-content p {
  color: #b0b3b8;
  line-height: 1.6;
}

.features-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Feature Visual */
.feature-visual {
  position: relative;
  width: 400px;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-circle {
  position: relative;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 20px 60px rgba(52, 152, 219, 0.3);
}

.center-logo {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
}

.orbit-item {
  position: absolute;
  width: 80px;
  height: 80px;
  background: rgba(52, 152, 219, 0.1);
  border: 2px solid #3498db;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: orbit 10s linear infinite;
}

.orbit-1 {
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.orbit-2 {
  top: 50%;
  right: -40px;
  transform: translateY(-50%);
  animation-delay: -3.33s;
}

.orbit-3 {
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: -6.66s;
}

.orbit-icon {
  font-size: 24px;
}

@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(160px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(160px) rotate(-360deg);
  }
}

/* Mobile App Section */
.mobile-app {
  background: #f8f9fa;
  padding: 120px 0;
  color: #2c3e50;
}

.mobile-app .container {
  flex-direction: column;
  gap: 60px;
}

.mobile-app .section-title {
  color: #2c3e50;
}

.section-subtitle {
  font-size: 18px;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 80px;
}

.app-showcase {
  display: flex;
  align-items: center;
  gap: 80px;
}

.app-phones {
  flex: 1;
  display: flex;
  gap: 40px;
  justify-content: center;
}

.phone-device {
  width: 240px;
  height: 480px;
  background: #000000;
  border-radius: 25px;
  padding: 6px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
  position: relative;
}

.phone-1 {
  transform: rotate(-5deg);
}

.phone-2 {
  transform: rotate(5deg);
}

.phone-screen-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 19px;
  overflow: hidden;
}

.app-interface {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-header {
  color: #3498db;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 1px solid #3498db;
  padding-bottom: 10px;
}

.energy-overview {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.energy-card {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.energy-label {
  color: #b0b3b8;
  font-size: 12px;
}

.energy-value {
  color: #27ae60;
  font-size: 16px;
  font-weight: 600;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.device-name {
  color: #ecf0f1;
  font-size: 12px;
}

.device-status {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #7f8c8d;
}

.device-status.on {
  background: #27ae60;
  box-shadow: 0 0 8px #27ae60;
}

.device-status.off {
  background: #e74c3c;
}

.control-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.control-item {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.control-icon {
  font-size: 20px;
}

.control-label {
  color: #ecf0f1;
  font-size: 10px;
}

.control-toggle {
  width: 30px;
  height: 16px;
  background: #7f8c8d;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s;
}

.control-toggle::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background: #ffffff;
  border-radius: 50%;
  transition: all 0.3s;
}

.control-toggle.active {
  background: #27ae60;
}

.control-toggle.active::before {
  transform: translateX(14px);
}

.app-features {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.app-feature {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.feature-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 4px;
}

.feature-dot.green {
  background: #27ae60;
  box-shadow: 0 0 10px rgba(39, 174, 96, 0.3);
}

.feature-dot.blue {
  background: #3498db;
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

.feature-dot.orange {
  background: #f39c12;
  box-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
}

.feature-text h4 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 8px;
}

.feature-text p {
  color: #7f8c8d;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 40px;
  }

  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 14px;
  }

  .features-content,
  .app-showcase {
    flex-direction: column;
    gap: 40px;
  }

  .house-3d {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  .phone-mockup {
    width: 240px;
    height: 480px;
  }

  .app-phones {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .phone-1,
  .phone-2 {
    transform: none;
  }

  .section-title {
    font-size: 28px;
  }

  .features-text h2 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 28px;
  }

  .section-title {
    font-size: 24px;
  }

  .features-text h2 {
    font-size: 24px;
  }

  .phone-mockup {
    width: 200px;
    height: 400px;
  }

  .phone-device {
    width: 200px;
    height: 400px;
  }

  .house-3d {
    max-width: 320px;
    height: 250px;
  }

  .visual-circle {
    width: 150px;
    height: 150px;
  }

  .center-logo {
    font-size: 24px;
  }

  .orbit-item {
    width: 60px;
    height: 60px;
  }

  .orbit-icon {
    font-size: 20px;
  }
}
</style>

<template>
  <div class="data-center">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-bg-placeholder"></div>
      </div>
      <div class="hero-content">
        <div class="container">
          <div class="hero-text">
            <h1 class="hero-title">
              数据中心智能管理方案，融合全流程技<br />
              术，让数据中心更智能、更高效、更安<br />
              全、更环保，降低运维成本。
            </h1>
            <p class="hero-subtitle">
              基于物联网、大数据、人工智能等先进技术，为数据中心提供全方位的智能化管理解决方案
            </p>
          </div>
          <div class="hero-image">
            <div class="server-rack">
              <div class="server-unit"></div>
              <div class="server-unit"></div>
              <div class="server-unit"></div>
              <div class="server-unit"></div>
              <div class="server-unit"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 3D Model Section -->
    <section class="model-section">
      <div class="container">
        <h2 class="section-title">因地制宜 分类指导</h2>

        <!-- 3D Model Display -->
        <div class="model-display">
          <div class="datacenter-3d">
            <!-- 3D数据中心模型 -->
            <div class="datacenter-room">
              <div class="server-rows">
                <div class="server-row"></div>
                <div class="server-row"></div>
                <div class="server-row"></div>
              </div>
              <div class="cooling-units">
                <div class="cooling-unit"></div>
                <div class="cooling-unit"></div>
              </div>
            </div>

            <!-- 功能标注点 -->
            <div class="annotation annotation-1">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>温度监控</span>
                <div class="annotation-detail">实时监控温度变化</div>
              </div>
            </div>

            <div class="annotation annotation-2">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>制冷系统</span>
                <div class="annotation-detail">智能制冷控制</div>
              </div>
            </div>

            <div class="annotation annotation-3">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>电源管理</span>
                <div class="annotation-detail">UPS电源监控</div>
              </div>
            </div>

            <div class="annotation annotation-4">
              <div class="annotation-dot"></div>
              <div class="annotation-label">
                <span>安全监控</span>
                <div class="annotation-detail">24小时安全监控</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">数据中心智能化管理核心功能</h2>
        <p class="section-subtitle">
          全面覆盖数据中心运营管理的各个环节，提升效率，降低成本
        </p>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🌡️</div>
            <h3>环境监控</h3>
            <p>实时监控机房温度、湿度、气压等环境参数，确保设备运行环境最优</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">❄️</div>
            <h3>制冷优化</h3>
            <p>智能制冷控制，提高制冷效率，降低能耗，延长设备使用寿命</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔋</div>
            <h3>UPS管理</h3>
            <p>不间断电源系统的智能管理和监控，确保数据中心供电稳定可靠</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Section -->
    <section class="technology">
      <div class="container">
        <div class="tech-content">
          <div class="tech-text">
            <h2>行业化管理与技术创新，科技、安全及<br />管理的优化整合</h2>
            <p>
              集成先进的监控技术、智能算法和管理系统，实现数据中心的全面智能化管理
            </p>

            <div class="tech-features">
              <div class="tech-feature">
                <h4>智能监控</h4>
                <p>24/7全天候智能监控系统，实时掌握设备状态</p>
              </div>
              <div class="tech-feature">
                <h4>预测维护</h4>
                <p>基于AI算法的预测性维护，提前发现潜在问题</p>
              </div>
              <div class="tech-feature">
                <h4>能效管理</h4>
                <p>智能能效管理系统，持续优化能源使用效率</p>
              </div>
            </div>
          </div>

          <div class="tech-image">
            <div class="laptop-display">
              <div class="laptop-screen">
                <div class="dashboard-content">
                  <div class="dashboard-header">数据中心管理系统</div>
                  <div class="dashboard-charts">
                    <div class="chart-item"></div>
                    <div class="chart-item"></div>
                    <div class="chart-item"></div>
                  </div>
                </div>
              </div>
              <div class="laptop-base"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Cases Section -->
    <section class="cases">
      <div class="container">
        <h2 class="section-title">数据中心应用场景</h2>
        <p class="section-subtitle">为不同类型的数据中心提供专业的解决方案</p>

        <div class="cases-grid">
          <div class="case-item">
            <div class="case-icon">🏢</div>
            <h3>企业数据中心</h3>
            <p>为企业级数据中心提供专业的智能化管理服务</p>
          </div>
          <div class="case-item">
            <div class="case-icon">☁️</div>
            <h3>云计算中心</h3>
            <p>支持大规模云计算数据中心的高效运营管理</p>
          </div>
          <div class="case-item">
            <div class="case-icon">📡</div>
            <h3>边缘数据中心</h3>
            <p>为边缘计算场景提供轻量化的智能管理方案</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 数据中心解决方案页面逻辑
</script>

<style scoped>
.data-center {
  padding-top: 130px;
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1b21 0%, #2c3e50 100%);
  opacity: 0.8;
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 80px;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 30px;
  color: #ffffff;
}

.hero-subtitle {
  font-size: 16px;
  color: #b0b3b8;
  line-height: 1.6;
  max-width: 500px;
}

.hero-image {
  flex: 0 0 400px;
}

/* Server Rack Styling */
.server-rack {
  width: 400px;
  height: 300px;
  background: #2a2b31;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  border: 2px solid #3498db;
  position: relative;
}

.server-unit {
  height: 40px;
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px;
  position: relative;
  border: 1px solid #5dade2;
}

.server-unit::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #27ae60;
  border-radius: 50%;
  box-shadow: 0 0 8px #27ae60;
}

.server-unit::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 2px;
  background: #ecf0f1;
  border-radius: 1px;
}

/* 3D Model Section */
.model-section {
  background: #2a2b31;
  padding: 120px 0;
}

.model-display {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 80px;
}

.datacenter-3d {
  position: relative;
  width: 600px;
  height: 400px;
  background: #3a3b41;
  border-radius: 12px;
  border: 2px solid #3498db;
  overflow: hidden;
}

.datacenter-room {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  position: relative;
  padding: 40px;
}

.server-rows {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 60%;
}

.server-row {
  height: 40px;
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px;
  border: 1px solid #5dade2;
  position: relative;
}

.server-row::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #27ae60;
  border-radius: 50%;
  box-shadow: 0 0 6px #27ae60;
}

.cooling-units {
  position: absolute;
  bottom: 40px;
  right: 40px;
  display: flex;
  gap: 10px;
}

.cooling-unit {
  width: 60px;
  height: 60px;
  background: #e74c3c;
  border-radius: 8px;
  border: 2px solid #c0392b;
  position: relative;
}

.cooling-unit::before {
  content: "❄️";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

/* Annotation Styles */
.annotation {
  position: absolute;
  z-index: 10;
}

.annotation-1 {
  top: 20%;
  left: 10%;
}

.annotation-2 {
  top: 20%;
  right: 10%;
}

.annotation-3 {
  bottom: 30%;
  left: 10%;
}

.annotation-4 {
  bottom: 30%;
  right: 10%;
}

.annotation-dot {
  width: 12px;
  height: 12px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.annotation-label {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
}

.annotation:hover .annotation-label {
  opacity: 1;
}

.annotation-detail {
  font-size: 10px;
  color: #b0b3b8;
  margin-top: 2px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Features Section */
.features {
  background: #f8f9fa;
  padding: 120px 0;
}

.features .section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 20px;
  color: #2c3e50;
}

.features .section-subtitle {
  font-size: 18px;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
}

/* Technology Section */
.technology {
  background: #2a2b31;
  padding: 120px 0;
}

.tech-content {
  display: flex;
  align-items: center;
  gap: 80px;
}

.tech-text {
  flex: 1;
}

.tech-text h2 {
  font-size: 36px;
  margin-bottom: 20px;
  color: #ffffff;
  line-height: 1.4;
}

.tech-text > p {
  font-size: 18px;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.tech-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-feature h4 {
  font-size: 20px;
  color: #3498db;
  margin-bottom: 8px;
}

.tech-feature p {
  color: #b0b3b8;
}

.tech-image {
  flex: 1;
}

/* Laptop Display Styling */
.laptop-display {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.laptop-screen {
  width: 100%;
  height: 300px;
  background: #1a1a1a;
  border-radius: 12px 12px 0 0;
  border: 8px solid #2c3e50;
  border-bottom: none;
  position: relative;
  overflow: hidden;
}

.laptop-base {
  width: 110%;
  height: 20px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-radius: 0 0 20px 20px;
  margin: 0 auto;
  position: relative;
  left: -5%;
}

.laptop-base::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 2px;
}

.dashboard-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  color: #3498db;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

.dashboard-charts {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.chart-item {
  height: 60px;
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
  border-radius: 6px;
  position: relative;
  border: 1px solid #5dade2;
}

.chart-item::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #27ae60;
  border-radius: 50%;
  box-shadow: 0 0 8px #27ae60;
}

.chart-item::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 100px;
  height: 3px;
  background: #ecf0f1;
  border-radius: 2px;
}

/* Cases Section */
.cases {
  background: #f8f9fa;
  padding: 120px 0;
}

.cases .section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 20px;
  color: #2c3e50;
}

.cases .section-subtitle {
  font-size: 18px;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 60px;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.case-item {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.case-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.case-item h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.case-item p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-center {
    padding-top: 140px;
  }

  .container {
    flex-direction: column;
    gap: 40px;
  }

  .hero-image {
    flex: none;
  }

  .server-placeholder {
    width: 100%;
    max-width: 400px;
  }

  .hero-title {
    font-size: 28px;
  }

  .model-display {
    flex-direction: column;
    gap: 40px;
  }

  .model-features {
    flex: none;
  }

  .datacenter-3d-placeholder {
    width: 100%;
    max-width: 500px;
  }

  .tech-content {
    flex-direction: column;
    gap: 40px;
  }

  .features-grid,
  .cases-grid {
    grid-template-columns: 1fr;
  }
}
</style>

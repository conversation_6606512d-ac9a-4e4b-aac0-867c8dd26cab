<template>
  <div class="about">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-graphics">
          <!-- Background Images -->
          <img src="/images/bg-green.png" alt="Green cube" class="bg-green" />
          <img
            src="/images/dotted.png"
            alt="Dotted frame"
            class="dotted-frame"
          />
          <img src="/images/pode.png" alt="Decoration" class="po-decoration" />
        </div>
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            在每度电都值得被精准管理的时<br />
            代，我们正在构建能源流动的智能<br />
            镜像世界
          </h1>
          <p class="hero-subtitle">
            CGC
            DIGITAL致力于通过先进的数字化技术，为全球客户提供智能化的能源管理解决方案，<br />
            构建更加高效、可持续的能源生态系统。
          </p>
          <p class="hero-description">
            我们相信，在数字化转型的浪潮中，每一度电都应该被精准管理，<br />
            每一个能源流动都应该被智能优化。
          </p>
        </div>
      </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics">
      <div class="content-container">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-number">15</div>
              <div class="stat-text">
                <div class="stat-sub">Years of <br />experience</div>
              </div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-number">36k</div>
              <div class="stat-text">
                <div class="stat-sub">
                  Satisfied clients <br />around the world
                </div>
              </div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-content">
              <div class="stat-number">6428</div>
              <div class="stat-text">
                <div class="stat-sub">
                  Project completed on <br />25 countries
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Choose CGC Section -->
    <section class="why-choose">
      <div class="content-container">
        <div class="why-choose-content">
          <div class="why-choose-left">
            <h2 class="section-title">为什么客户选择CGC DIGITAL</h2>
            <p class="section-description">
              我们拥有深厚的行业积淀和前沿的技术实力，为客户提供从咨询规划到实施运维的全生命周期服务，
              帮助企业实现数字化转型和可持续发展目标。
            </p>
            <div class="company-info">
              <div class="logo-section"></div>
            </div>
          </div>
          <div class="why-choose-right">
            <div class="choose-circles">
              <div class="circle-grid">
                <div class="circle-item circle-1">
                  <div class="circle-content">
                    <div class="circle-text">智能运维</div>
                  </div>
                </div>
                <div class="circle-item circle-2">
                  <div class="circle-content">
                    <div class="circle-text">全域智控</div>
                  </div>
                </div>
                <div class="circle-item circle-3">
                  <div class="circle-content">
                    <div class="circle-text">实时监控</div>
                  </div>
                </div>
                <div class="circle-item circle-4">
                  <div class="circle-content">
                    <div class="circle-text">数据分析</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Video Section -->
    <section class="video-section">
      <div class="content-container">
        <div class="video-container">
          <div class="video-placeholder">
            <img
              src="/images/关于我们.png"
              alt="Company Video"
              class="video-thumbnail"
            />
            <div class="play-button">
              <div class="play-icon">▶</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Work Process Section -->
    <section class="work-process">
      <div class="content-container">
        <h2 class="process-title">
          We always follow the standard work process
        </h2>
        <p class="process-description">
          从需求分析到最终部署的标准化流程，让每个项目都能顺利交付
        </p>
        <div class="process-flow">
          <!-- Top Row -->
          <div class="process-row top-row">
            <div class="process-item">
              <div class="process-icon">
                <span class="icon-number">01</span>
              </div>
              <h3>DEFINE</h3>
              <div class="process-details">
                <p>Requirements Development</p>
                <p>Portable Metering Studies</p>
                <p>Power Quality Audits</p>
              </div>
            </div>
            <div class="process-arrow right-arrow">→</div>
            <div class="process-item">
              <div class="process-icon">
                <span class="icon-number">02</span>
              </div>
              <h3>DESIGN</h3>
              <div class="process-details">
                <p>Network Infrastructure</p>
                <p>Control System</p>
                <p>System & DER Integration</p>
              </div>
            </div>
            <div class="process-arrow right-arrow">→</div>
            <div class="process-item">
              <div class="process-icon">
                <span class="icon-number">03</span>
              </div>
              <h3>DEPLOY</h3>
              <div class="process-details">
                <p>IP Networking &Equipment</p>
                <p>with SD-WAN</p>
                <p>SCADA/PMS System</p>
              </div>
            </div>
          </div>

          <!-- Connecting Arrow -->
          <div class="process-connector">
            <div class="connector-arrow down-arrow">↓</div>
          </div>

          <!-- Bottom Row -->
          <div class="process-row bottom-row">
            <div class="process-item">
              <div class="process-icon">
                <span class="icon-number">06</span>
              </div>
              <h3>COMMISSION</h3>
              <div class="process-details">
                <p>Cloud Infrastructure</p>
                <p>Data Interop & Integrations</p>
                <p>Startup, Train & Handoff</p>
              </div>
            </div>
            <div class="process-arrow left-arrow">←</div>
            <div class="process-item">
              <div class="process-icon">
                <span class="icon-number">05</span>
              </div>
              <h3>SUPPORT</h3>
              <div class="process-details">
                <p>Fleet Management</p>
                <p>Cybersecurity Assurance</p>
                <p>Maintenance & Ops Support</p>
              </div>
            </div>
            <div class="process-arrow left-arrow">←</div>
            <div class="process-item">
              <div class="process-icon">
                <span class="icon-number">04</span>
              </div>
              <h3>OPTIMIZE</h3>
              <div class="process-details">
                <p>Data Quality/Advisory</p>
                <p>Services</p>
                <p>Power Quality Advisory</p>
                <p>Services</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="content-container">
        <div class="cta-content">
          <h2 class="cta-title">
            Eliminate energy waste with AI-optimized power systems
          </h2>
          <button class="cta-button">LEARN MORE</button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// About page logic
</script>

<style scoped>
.about {
  padding-top: 80px;
  min-height: 100vh;
  background: #1e1f25;
  color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  position: relative;
  padding: 120px 0 80px;
  background: #1e1f25;
}

.hero-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  z-index: 1;
}

.hero-graphics {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Background Images */
.bg-green {
  position: absolute;
  top: 0%;
  right: 70%;
  width: 800px;
  height: auto;
  z-index: 3;
}

.dotted-frame {
  position: absolute;
  top: -5%;
  right: 20%;
  width: 320px;
  height: auto;
  z-index: 1;
  opacity: 0.4;
}

.po-decoration {
  position: absolute;
  top: 25%;
  right: 28%;
  width: 400px;
  height: auto;
  z-index: 4;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-text {
  max-width: 65%;
}

.hero-title {
  font-size: 3.2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  color: white;
}

.hero-subtitle {
  font-size: 1.1rem;
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 20px;
}

.hero-description {
  font-size: 1rem;
  color: #888;
  line-height: 1.6;
}

/* Statistics Section */
.statistics {
  margin-top: 80px;
  padding: 80px 20px;
  background: #1e1f25;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.stat-item {
  padding: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: center;
}

.stat-number {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-text {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.stat-sub {
  font-size: 1rem;
  color: #888;
  line-height: 1.2;
}

/* Why Choose Section */
.why-choose {
  padding: 100px 0;
  background: #1e1f25;
}

.why-choose-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: white;
  line-height: 1.3;
}

.section-description {
  font-size: 1.1rem;
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 40px;
}

.company-logo {
  width: 120px;
  height: auto;
}

.choose-circles {
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-grid {
  position: relative;
  width: 400px;
  height: 300px;
  margin: 0 auto;
}

.circle-item {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  transition: transform 0.3s ease;
  background: rgba(47, 50, 65, 0.5);
  border: 2px solid #4c6ef5;
}

.circle-item:hover {
  transform: scale(1.05);
}

.circle-1 {
  width: 180px;
  height: 180px;
  top: 20px;
  left: 50px;
  border: 3px solid #4c6ef5;
}

.circle-2 {
  width: 140px;
  height: 140px;
  top: 30px;
  right: 30px;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 80px;
  right: 80px;
}

.circle-4 {
  width: 120px;
  height: 120px;
  bottom: 20px;
  left: 20px;
}

.circle-content {
  text-align: center;
}

.circle-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Video Section */
.video-section {
  padding: 80px 0;
  background: #1e1f25;
}

.video-container {
  max-width: 800px;
  margin: 0 auto;
}

.video-placeholder {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: auto;
  display: block;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-button:hover {
  background: white;
  transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
  font-size: 2rem;
  color: #1e1f25;
  margin-left: 5px;
}

/* Work Process Section */
.work-process {
  padding: 100px 0;
  background: #1e1f25;
}

.process-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: white;
}

.process-description {
  text-align: center;
  color: #b0b0b0;
  font-size: 1.1rem;
  margin-bottom: 80px;
  line-height: 1.6;
}

.process-flow {
  max-width: 1200px;
  margin: 0 auto;
}

.process-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
}

.process-row.bottom-row {
  flex-direction: row;
}

.process-connector {
  display: flex;
  justify-content: flex-end;
  margin-right: 100px;
  margin-bottom: 40px;
}

.process-item {
  text-align: center;
  padding: 30px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  transition: transform 0.3s ease;
  min-width: 250px;
}

.process-item:hover {
  transform: translateY(-10px);
}

.process-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4c6ef5;
  border-radius: 50%;
}

.icon-number {
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
}

.process-item h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.process-details p {
  color: #b0b0b0;
  line-height: 1.4;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.process-arrow,
.right-arrow,
.left-arrow,
.down-arrow {
  font-size: 2rem;
  color: #4c6ef5;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #4c6ef5 0%, #339af0 100%);
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 40px;
  line-height: 1.3;
}

.cta-button {
  background: transparent;
  border: 2px solid white;
  color: white;
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cta-button:hover {
  background: white;
  color: #4c6ef5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-text {
    max-width: 70%;
  }

  .why-choose-content {
    gap: 60px;
  }

  .process-row {
    flex-direction: column;
    gap: 30px;
  }

  .process-connector {
    margin-right: 0;
    justify-content: center;
  }

  .right-arrow,
  .left-arrow {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-text {
    max-width: 100%;
  }

  .bg-green,
  .dotted-frame,
  .po-decoration {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .stat-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .stat-text {
    text-align: center;
  }

  .why-choose-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .circle-grid {
    width: 300px;
    height: 250px;
  }

  .circle-1 {
    width: 140px;
    height: 140px;
    top: 10px;
    left: 30px;
  }

  .circle-2 {
    width: 110px;
    height: 110px;
    top: 20px;
    right: 20px;
  }

  .circle-3 {
    width: 80px;
    height: 80px;
    bottom: 60px;
    right: 60px;
  }

  .circle-4 {
    width: 100px;
    height: 100px;
    bottom: 10px;
    left: 10px;
  }

  .circle-text {
    font-size: 1rem;
  }

  .process-row {
    flex-direction: column;
    gap: 20px;
  }

  .process-item {
    min-width: auto;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .process-connector {
    display: none;
  }

  .right-arrow,
  .left-arrow,
  .down-arrow {
    display: none;
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .container {
    padding: 0 15px;
  }
}
</style>

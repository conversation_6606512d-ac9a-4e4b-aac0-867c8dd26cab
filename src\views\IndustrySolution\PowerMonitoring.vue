<template>
  <div class="power-monitoring-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            三维智能联动平台实现全球精准决策与毫秒级响应。
          </h1>
          <p class="hero-subtitle">
            构建变电站级三维孪生体，融合SCADA实时数据与设备历史档案，实现从主变压器到绝缘子的全生命周期健康管理，支持设备拆解透视与故障回溯
          </p>
          <button class="hero-button">See more</button>
        </div>
        <div class="hero-image">
          <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop" alt="Control Room" class="control-room-image">
        </div>
      </div>
    </section>

    <!-- 3D Scenario Section -->
    <section class="scenario-3d-section">
      <div class="container">
        <h2 class="section-title-dark">覆盖配电的设计、建造、运营、维护的全场景应用</h2>
        <div class="scenario-3d-content">
          <div class="isometric-view">
            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop" alt="3D Control Room Scene" class="isometric-image">

            <!-- Annotation Points -->
            <div class="annotation annotation-1">
              <div class="annotation-dot"></div>
              <div class="annotation-popup">
                <div class="popup-header">设备监控</div>
                <div class="popup-content">实时监控设备状态</div>
              </div>
            </div>

            <div class="annotation annotation-2">
              <div class="annotation-dot"></div>
              <div class="annotation-popup">
                <div class="popup-header">数据分析</div>
                <div class="popup-content">智能数据处理分析</div>
              </div>
            </div>

            <div class="annotation annotation-3">
              <div class="annotation-dot"></div>
              <div class="annotation-popup">
                <div class="popup-header">故障预警</div>
                <div class="popup-content">提前预警潜在故障</div>
              </div>
            </div>

            <div class="annotation annotation-4">
              <div class="annotation-dot"></div>
              <div class="annotation-popup">
                <div class="popup-header">远程控制</div>
                <div class="popup-content">远程设备操作控制</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title-light">在国际局势复杂多变与低碳发展双重驱动下，依托自主可控技术保障电力系统稳定高效运行</h2>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <img src="https://via.placeholder.com/80x80/e74c3c/ffffff?text=🔧" alt="Feature 1" class="feature-icon-img">
            </div>
            <h3>先进的边缘计算能力</h3>
            <p>基于边缘计算技术，实现毫秒级数据处理和响应，确保系统实时性和可靠性</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <img src="https://via.placeholder.com/80x80/3498db/ffffff?text=⚡" alt="Feature 2" class="feature-icon-img">
            </div>
            <h3>灵活的模块化设计</h3>
            <p>采用模块化架构设计，支持灵活配置和扩展，适应不同规模的电力系统需求</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <img src="https://via.placeholder.com/80x80/27ae60/ffffff?text=📊" alt="Feature 3" class="feature-icon-img">
            </div>
            <h3>强大的数据处理能力</h3>
            <p>集成大数据分析和人工智能算法，提供智能化的数据分析和决策支持</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <img src="https://via.placeholder.com/80x80/f39c12/ffffff?text=🛡️" alt="Feature 4" class="feature-icon-img">
            </div>
            <h3>全面的安全保障</h3>
            <p>多层次安全防护体系，确保电力系统运行安全和数据信息安全</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Digital Process Section -->
    <section class="digital-process-section">
      <div class="container">
        <h2 class="section-title">全面打通连接、监测、治理的全数字化<br/>流程，形成智能配电闭环价值落地</h2>
        <div class="process-content">
          <div class="process-text">
            <h3>数字化转型</h3>
            <p>通过先进的数字化技术，实现电力系统的智能化管理和优化运行</p>
            <ul>
              <li>实时数据采集与监控</li>
              <li>智能分析与预测</li>
              <li>自动化控制与调节</li>
              <li>全生命周期管理</li>
            </ul>
          </div>
          <div class="process-image">
            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=500&fit=crop" alt="Digital Process Laptop" class="laptop-image">
          </div>
        </div>
      </div>
    </section>

    <!-- Application Scenarios Section -->
    <section class="app-scenarios-section">
      <div class="container">
        <h2 class="section-title-light">电力监控系统应用场景</h2>
        <p class="section-subtitle-light">三大类电力用户，N种安定设备，N种解决方案</p>
        <div class="scenarios-grid">
          <div class="scenario-card">
            <img src="https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400&h=250&fit=crop" alt="老旧电网" class="scenario-card-image">
            <div class="scenario-card-content">
              <h3>老旧电网</h3>
              <p>设备智能化改造，提高效率50%</p>
              <div class="scenario-details">
                <span>智能化升级</span>
                <span>效率提升</span>
              </div>
            </div>
          </div>
          <div class="scenario-card">
            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=250&fit=crop" alt="城市变电站" class="scenario-card-image">
            <div class="scenario-card-content">
              <h3>城市变电站</h3>
              <p>三维孪生可视化，辅助决策</p>
              <div class="scenario-details">
                <span>可视化管理</span>
                <span>智能决策</span>
              </div>
            </div>
          </div>
          <div class="scenario-card">
            <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop" alt="新建电厂" class="scenario-card-image">
            <div class="scenario-card-content">
              <h3>新建电厂</h3>
              <p>数字化生产平台，运维成本降低30%</p>
              <div class="scenario-details">
                <span>数字化平台</span>
                <span>成本优化</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Page logic for Power Monitoring Solution
</script>

<style scoped>
.power-monitoring-page {
  background-color: #000;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
}

.section-title-dark {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.section-title-light {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #000;
  line-height: 1.4;
}

.section-subtitle-light {
  font-size: 1.1rem;
  text-align: center;
  color: #666;
  margin-bottom: 4rem;
}

/* Hero Section */
.hero-section {
  min-height: 80vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2c3e50 100%);
  display: flex;
  align-items: center;
  padding: 6rem 0;
}

.hero-section .container {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.hero-content {
  flex: 1;
  max-width: 50%;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
  color: #fff;
}

.hero-subtitle {
  font-size: 1.1rem;
  margin-bottom: 2.5rem;
  line-height: 1.8;
  color: #ccc;
}

.hero-button {
  background-color: #c9302c;
  color: white;
  padding: 12px 30px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.hero-button:hover {
  background-color: #d9534f;
}

.hero-image {
  flex: 1;
  text-align: center;
}

.control-room-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* 3D Scenario Section */
.scenario-3d-section {
  background-color: #f8f9fa;
  padding: 8rem 0;
}

.scenario-3d-content {
  text-align: center;
}

.isometric-view {
  position: relative;
  display: inline-block;
  margin: 2rem 0;
}

.isometric-image {
  max-width: 800px;
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

/* Annotation Points */
.annotation {
  position: absolute;
  cursor: pointer;
}

.annotation-1 {
  top: 20%;
  left: 15%;
}

.annotation-2 {
  top: 25%;
  right: 20%;
}

.annotation-3 {
  bottom: 30%;
  left: 25%;
}

.annotation-4 {
  bottom: 35%;
  right: 15%;
}

.annotation-dot {
  width: 16px;
  height: 16px;
  background: #e74c3c;
  border: 3px solid #fff;
  border-radius: 50%;
  animation: pulse 2s infinite;
  position: relative;
  z-index: 2;
}

.annotation-popup {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.annotation:hover .annotation-popup {
  opacity: 1;
  visibility: visible;
}

.popup-header {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.popup-content {
  font-size: 12px;
  color: #666;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}


/* Features Section */
.features-section {
  background-color: #f8f9fa;
  color: #000;
  padding: 8rem 0;
}

.features-section .section-title-light {
  font-size: 1.8rem;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 5rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 3rem;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-item {
  text-align: center;
  padding: 2rem 1rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.feature-icon {
  margin-bottom: 1.5rem;
}

.feature-icon-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

.feature-item h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.feature-item p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.6;
}

/* Digital Process Section */
.digital-process-section {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 8rem 0;
  position: relative;
}

.digital-process-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, rgba(248,249,250,1) 0%, rgba(248,249,250,0) 100%);
}

.process-content {
  display: flex;
  align-items: center;
  gap: 4rem;
  margin-top: 3rem;
}

.process-text {
  flex: 1;
  color: #fff;
}

.process-text h3 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #fff;
}

.process-text p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: #ccc;
  line-height: 1.6;
}

.process-text ul {
  list-style: none;
  padding: 0;
}

.process-text li {
  padding: 0.8rem 0;
  color: #ccc;
  position: relative;
  padding-left: 2rem;
}

.process-text li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #27ae60;
  font-weight: bold;
}

.process-image {
  flex: 1;
  text-align: center;
}

.laptop-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

/* Application Scenarios Section */
.app-scenarios-section {
  background-color: #f8f9fa;
  color: #000;
  padding: 8rem 0;
  position: relative;
}

.app-scenarios-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, rgba(26,26,46,1) 0%, rgba(26,26,46,0) 100%);
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2.5rem;
  margin-top: 3rem;
}

.scenario-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.scenario-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.scenario-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.scenario-card-content {
  padding: 2rem;
}

.scenario-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.scenario-card p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.scenario-details {
  display: flex;
  gap: 0.5rem;
}

.scenario-details span {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-section .container {
    flex-direction: column;
    text-align: center;
  }

  .hero-content {
    max-width: 100%;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .process-content {
    flex-direction: column;
    gap: 3rem;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title,
  .section-title-dark,
  .section-title-light {
    font-size: 1.8rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .isometric-image {
    max-width: 100%;
  }

  .annotation-1 {
    top: 15%;
    left: 10%;
  }

  .annotation-2 {
    top: 20%;
    right: 10%;
  }

  .annotation-3 {
    bottom: 25%;
    left: 15%;
  }

  .annotation-4 {
    bottom: 30%;
    right: 10%;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .section-title,
  .section-title-dark,
  .section-title-light {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 15px;
  }

  .hero-section,
  .scenario-3d-section,
  .features-section,
  .digital-process-section,
  .app-scenarios-section {
    padding: 4rem 0;
  }

  .feature-item {
    padding: 1.5rem 1rem;
  }

  .scenario-card-content {
    padding: 1.5rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 40px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-subtitle {
    font-size: 14px;
  }

  .tech-content {
    flex-direction: column;
    gap: 40px;
  }

  .power-system-3d {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  .control-room {
    height: 300px;
  }

  .monitoring-screens {
    height: 150px;
  }

  .control-panels {
    height: 100px;
  }

  .features-grid,
  .cases-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 28px;
  }

  .tech-text h2 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 24px;
  }

  .section-title {
    font-size: 24px;
  }

  .tech-text h2 {
    font-size: 24px;
  }

  .power-system-3d {
    max-width: 320px;
    height: 250px;
  }

  .control-room {
    height: 250px;
  }
}
</style>

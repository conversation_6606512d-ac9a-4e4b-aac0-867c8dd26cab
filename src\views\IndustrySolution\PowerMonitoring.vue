<template>
  <div class="power-monitoring-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          三维智能联动平台实现全球精准决策与毫秒级响应。
        </h1>
        <p class="hero-subtitle">
          构建变电站级三维孪生体,融合SCADA实时数据与设备
          历史档案,实现从主变压器到绝缘子的全生命周期健康管
          理,支持设备拆解透视与故障回溯
        </p>
        <button class="hero-button">See more</button>
      </div>
    </section>

    <!-- All-Scenario Section -->
    <section class="scenario-section">
      <h2 class="section-title">覆盖配电的设计、建造、运营、维护的全场景应用</h2>
      <div class="scenario-content">
        <!-- Image placeholder -->
        <!-- <img src="/images/scenario-placeholder.png" alt="Power Distribution Scenario" class="scenario-image"> -->
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <h2 class="section-title-light">在国际局势复杂多变与低碳发展双重驱动下,依托自主可控技术保障电力系统稳定高效运行</h2>
        <div class="features-grid">
            <div class="feature-item">
                <!-- Icon placeholder -->
                <!-- <img src="/images/icon-placeholder-1.svg" alt="Feature 1 Icon" class="feature-icon"> -->
                <p>先进的边缘计算能力</p>
            </div>
            <div class="feature-item">
                <!-- Icon placeholder -->
                <!-- <img src="/images/icon-placeholder-2.svg" alt="Feature 2 Icon" class="feature-icon"> -->
                <p>灵活的模块化设计</p>
            </div>
            <div class="feature-item">
                <!-- Icon placeholder -->
                <!-- <img src="/images/icon-placeholder-3.svg" alt="Feature 3 Icon" class="feature-icon"> -->
                <p>强大的数据处理能力</p>
            </div>
        </div>
    </section>

    <!-- Digital Process Section -->
    <section class="digital-process-section">
      <h2 class="section-title">全面打通连接、监测、治理的全数字化<br/>流程,形成智能配电闭环价值落地</h2>
      <div class="process-content">
        <!-- Image placeholder -->
        <!-- <img src="/images/digital-process-placeholder.png" alt="Digital Process Laptop" class="process-image"> -->
      </div>
    </section>

    <!-- Application Scenarios Section -->
    <section class="app-scenarios-section">
      <h2 class="section-title-light">电力监控系统应用场景</h2>
      <p class="section-subtitle-light">三大类电力用户,N种安定设备,N种解决方案</p>
      <div class="scenarios-grid">
        <div class="scenario-card">
          <!-- Image placeholder -->
          <!-- <img src="/images/old-grid-placeholder.png" alt="老旧电网" class="scenario-card-image"> -->
          <h3>老旧电网</h3>
          <p>设备智能化改造，提高效率50%</p>
        </div>
        <div class="scenario-card">
          <!-- Image placeholder -->
          <!-- <img src="/images/substation-placeholder.png" alt="城市变电站" class="scenario-card-image"> -->
          <h3>城市变电站</h3>
          <p>三维孪生可视化，辅助决策</p>
        </div>
        <div class="scenario-card">
          <!-- Image placeholder -->
          <!-- <img src="/images/new-plant-placeholder.png" alt="新建电厂" class="scenario-card-image"> -->
          <h3>新建电厂</h3>
          <p>数字化生产平台，运维成本降低30%</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Page logic for Power Monitoring Solution
</script>

<style scoped>
.power-monitoring-page {
  background-color: #000;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.section-title {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #fff;
  line-height: 1.4;
}

.section-title-light {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 4rem;
  font-weight: 500;
  color: #000;
  line-height: 1.4;
}

.section-subtitle-light {
  font-size: 1.1rem;
  text-align: center;
  color: #666;
  margin-bottom: 4rem;
}

/* Hero Section */
.hero-section {
  height: 80vh;
  /* Placeholder for background image */
   background-image: url('/public/images/<EMAIL>');  
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0 15%;
  text-align: left;
}

.hero-content {
    max-width: 45%;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.hero-subtitle {
  font-size: 1rem;
  margin-bottom: 2.5rem;
  max-width: 550px;
  line-height: 1.8;
  color: #ccc;
}

.hero-button {
  background-color: #c9302c;
  color: white;
  padding: 10px 25px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.hero-button:hover {
  background-color: #d9534f;
}

/* Common Section Styling */
.scenario-section, .features-section, .digital-process-section, .app-scenarios-section {
  padding: 6rem 15%;
}

/* Scenario Section */
.scenario-section {
    background-color: #1a1a1a;
}

.scenario-content {
  text-align: center;
}

.scenario-image {
  max-width: 70%;
  height: auto;
}

/* Features Section */
.features-section {
    background-color: #f4f4f4;
    color: #000;
}

.features-section .section-title-light {
    font-size: 1.8rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5rem;
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    font-size: 1.1rem;
}

.feature-icon {
  width: 60px;
  height: 60px;
}

/* Digital Process Section */
.digital-process-section {
    background-color: #1a1a1a;
    /* background-image: url('/images/digital-bg-wave.png'); */
    background-repeat: no-repeat;
    background-position: bottom center;
    background-size: 100%;
    padding-bottom: 15rem;
}

.digital-process-section .section-title {
    line-height: 1.5;
}

.process-content {
  text-align: center;
}

.process-image {
  max-width: 80%;
  height: auto;
  border-radius: 8px;
}

/* Application Scenarios Section */
.app-scenarios-section {
    background-color: #f4f4f4;
    color: #000;
    padding-top: 10rem;
    /* background-image: url('/images/app-bg-wave.png'); */
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100%;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.scenario-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  text-align: center;
  padding-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.scenario-card-image {
  width: 100%;
  height: auto;
  margin-bottom: 1.5rem;
}

.scenario-card h3 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.scenario-card p {
  font-size: 1rem;
  color: #666;
}



.data-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  justify-content: center;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.data-label {
  color: #b0b3b8;
  font-size: 12px;
}

.data-value {
  color: #27ae60;
  font-size: 14px;
  font-weight: 600;
}

/* Model Section */
.model-section {
  background: #2a2b31;
  padding: 120px 0;
}

.section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 80px;
  color: #ffffff;
  line-height: 1.4;
}

.model-display {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}

.power-system-3d {
  position: relative;
  width: 600px;
  height: 400px;
  background: #3a3b41;
  border-radius: 12px;
  border: 2px solid #3498db;
  overflow: hidden;
  perspective: 1000px;
}

.power-station {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  position: relative;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.transformer-area {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  transform: rotateX(15deg) rotateY(-10deg);
}

.transformer {
  width: 60px;
  height: 80px;
  background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
  border-radius: 8px;
  border: 2px solid #f39c12;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.transformer::before {
  content: "";
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  background: #f1c40f;
  border-radius: 50%;
  box-shadow: 0 0 10px #f1c40f;
}

.distribution-lines {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
  transform: rotateX(15deg) rotateY(-10deg);
}

.power-line {
  width: 200px;
  height: 4px;
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
  border-radius: 2px;
  position: relative;
  animation: electricFlow 3s infinite;
}

.power-line:nth-child(2) {
  animation-delay: 1s;
}

.power-line:nth-child(3) {
  animation-delay: 2s;
}

@keyframes electricFlow {
  0%,
  100% {
    box-shadow: 0 0 5px #3498db;
  }
  50% {
    box-shadow: 0 0 15px #3498db, 0 0 25px #3498db;
  }
}

.control-building {
  width: 80px;
  height: 60px;
  background: linear-gradient(90deg, #95a5a6 0%, #7f8c8d 100%);
  border-radius: 6px;
  border: 2px solid #bdc3c7;
  transform: rotateX(15deg) rotateY(-10deg);
  position: relative;
}

.control-building::before {
  content: "";
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  background: #2c3e50;
  border-radius: 2px;
}

/* Annotation Styles */
.annotation {
  position: absolute;
  z-index: 10;
}

.annotation-1 {
  top: 20%;
  left: 20%;
}

.annotation-2 {
  top: 20%;
  right: 20%;
}

.annotation-3 {
  bottom: 30%;
  left: 20%;
}

.annotation-4 {
  bottom: 30%;
  right: 20%;
}

.annotation-dot {
  width: 12px;
  height: 12px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.annotation-label {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
}

.annotation:hover .annotation-label {
  opacity: 1;
}

.annotation-detail {
  font-size: 10px;
  color: #b0b3b8;
  margin-top: 2px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Features Section */
.features {
  background: #f8f9fa;
  padding: 120px 0;
}

.features .section-title {
  color: #2c3e50;
}

.section-subtitle {
  font-size: 18px;
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 80px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
}

/* Technology Section */
.technology {
  background: #2a2b31;
  padding: 120px 0;
}

.tech-content {
  display: flex;
  align-items: center;
  gap: 80px;
}

.tech-text {
  flex: 1;
}

.tech-text h2 {
  font-size: 36px;
  margin-bottom: 20px;
  color: #ffffff;
  line-height: 1.4;
}

.tech-text > p {
  font-size: 18px;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.tech-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-feature h4 {
  font-size: 20px;
  color: #3498db;
  margin-bottom: 8px;
}

.tech-feature p {
  color: #b0b3b8;
}

.tech-image {
  flex: 1;
}

/* Monitoring System Display Styling */
.monitoring-system-display {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.system-screen {
  width: 100%;
  height: 400px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 3px solid #3498db;
  overflow: hidden;
  position: relative;
}

.system-interface {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.system-header {
  color: #3498db;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 1px solid #3498db;
  padding-bottom: 10px;
}

.monitoring-dashboard {
  flex: 1;
  display: flex;
  gap: 20px;
}

.dashboard-left {
  flex: 1;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(52, 152, 219, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.power-diagram {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.power-flow {
  height: 6px;
  background: linear-gradient(90deg, #e74c3c 0%, #f39c12 50%, #27ae60 100%);
  border-radius: 3px;
  animation: powerFlowDiagram 3s infinite;
}

.power-flow:nth-child(2) {
  animation-delay: 1s;
}

.power-flow:nth-child(3) {
  animation-delay: 2s;
}

@keyframes powerFlowDiagram {
  0%,
  100% {
    opacity: 0.5;
    transform: scaleX(0.7);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

.dashboard-right {
  flex: 1;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.status-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.status-label {
  color: #ecf0f1;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #7f8c8d;
  transition: all 0.3s;
}

.status-indicator.active {
  background: #27ae60;
  box-shadow: 0 0 8px #27ae60;
}

.status-indicator.warning {
  background: #f39c12;
  box-shadow: 0 0 8px #f39c12;
}

/* Cases Section */
.cases {
  background: #f8f9fa;
  padding: 120px 0;
}

.cases .section-title {
  color: #2c3e50;
}

.cases .section-subtitle {
  color: #7f8c8d;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.case-item {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.case-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.case-item h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.case-item p {
  color: #7f8c8d;
  line-height: 1.6;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 40px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-subtitle {
    font-size: 14px;
  }

  .tech-content {
    flex-direction: column;
    gap: 40px;
  }

  .power-system-3d {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  .control-room {
    height: 300px;
  }

  .monitoring-screens {
    height: 150px;
  }

  .control-panels {
    height: 100px;
  }

  .features-grid,
  .cases-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 28px;
  }

  .tech-text h2 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 24px;
  }

  .section-title {
    font-size: 24px;
  }

  .tech-text h2 {
    font-size: 24px;
  }

  .power-system-3d {
    max-width: 320px;
    height: 250px;
  }

  .control-room {
    height: 250px;
  }
}
</style>

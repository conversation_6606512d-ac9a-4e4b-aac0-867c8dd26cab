<template>
  <div class="asset-management">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <img
          src="/images/<EMAIL>"
          alt="产品中心背景"
          class="hero-bg-image"
        />
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            设备全生命周期管理平台，<br />
            让电力资产专业运行
          </h1>
          <p class="hero-subtitle">
            基于物联网、大数据、人工智能等先进技术，为电力行业提供全方位的设备管理解决方案
          </p>
        </div>
        <div class="hero-dashboard">
          <img
            src="/images/<EMAIL>"
            alt="管理平台预览"
            class="dashboard-image"
          />
        </div>
      </div>
    </section>

    <!-- Standards Section -->
    <section class="standards">
      <div class="container">
        <h2 class="standards-title">
          We always follow the standard work process
        </h2>
        <p class="standards-subtitle">
          严格遵循标准化工作流程，确保产品质量与服务水准
        </p>

        <div class="standards-grid">
          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">01</div>
            </div>
            <h3>需求分析</h3>
            <p>深入了解客户需求，制定个性化解决方案</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">02</div>
            </div>
            <h3>方案设计</h3>
            <p>基于需求分析结果，设计最优的技术方案</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">03</div>
            </div>
            <h3>产品实施</h3>
            <p>严格按照设计方案进行产品开发和部署</p>
          </div>

          <div class="standard-item">
            <div class="standard-icon">
              <div class="icon-circle">04</div>
            </div>
            <h3>测试验收</h3>
            <p>全面测试产品功能，确保达到预期效果</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <div class="feature-grid">
          <div class="feature-card">
            <h3>资产台账</h3>
            <p>建立完整的资产档案，实现资产信息的数字化管理</p>
          </div>

          <div class="feature-card">
            <h3>状态监测</h3>
            <p>实时监测资产运行状态，及时发现潜在问题</p>
          </div>

          <div class="feature-card">
            <h3>维护管理</h3>
            <p>制定科学的维护计划，延长资产使用寿命</p>
          </div>

          <div class="feature-card">
            <h3>价值评估</h3>
            <p>动态评估资产价值，为决策提供数据支持</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 资产管理页面逻辑
</script>

<style scoped>
.asset-management {
  padding-top: 130px; /* 为主导航+二级导航预留空间 */
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1920px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  gap: 80px;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
  color: #ffffff;
}

.hero-subtitle {
  font-size: 18px;
  line-height: 1.6;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.hero-dashboard {
  flex: 1;
  text-align: center;
}

.dashboard-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Standards Section */
.standards {
  background: #2a2b31;
  padding: 120px 0;
}

.container {
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 20px;
}

.standards-title {
  font-size: 48px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: #ffffff;
}

.standards-subtitle {
  font-size: 18px;
  text-align: center;
  color: #b0b3b8;
  margin-bottom: 80px;
}

.standards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 60px;
  max-width: 1200px;
  margin: 0 auto;
}

.standard-item {
  text-align: center;
}

.standard-icon {
  margin-bottom: 30px;
}

.icon-circle {
  width: 80px;
  height: 80px;
  background: #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 auto;
}

.standard-item h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffffff;
}

.standard-item p {
  font-size: 16px;
  line-height: 1.6;
  color: #b0b3b8;
}

/* Features Section */
.features {
  padding: 120px 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: #2a2b31;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(52, 152, 219, 0.2);
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #3498db;
  font-weight: 600;
}

.feature-card p {
  color: #b0b3b8;
  line-height: 1.6;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-content {
    max-width: 1200px;
    gap: 60px;
  }

  .standards-grid {
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .asset-management {
    padding-top: 140px;
  }

  .hero {
    height: auto;
    padding: 60px 0;
  }

  .hero-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .standards-title {
    font-size: 36px;
  }

  .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 28px;
  }

  .standards-title {
    font-size: 28px;
  }

  .standards-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
</style>

<template>
  <div class="contact">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-graphics">
          <!-- 3D Graphics Elements -->
          <div class="graphic-element cube-red"></div>
          <div class="graphic-element cube-green"></div>
          <div class="graphic-element cube-white"></div>
          <div class="graphic-element sphere"></div>
        </div>
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">Contact Us</h1>
          <p class="hero-subtitle">
            CGC DIGITAL provides a variety of<br />
            services covering the lifecycle of your<br />
            project
          </p>
        </div>
      </div>
    </section>

    <!-- Contact Content Section -->
    <section class="contact-content">
      <div class="content-container">
        <div class="contact-grid">
          <!-- Contact Info -->
          <div class="contact-info">
            <h2 class="section-title">Head office</h2>
            <div class="office-info">
              <div class="office-address">
                <p>A15 Building</p>
                <p>74 Ba Trieu St, Hoan Kiem Dist.</p>
                <p>Hanoi, Vietnam</p>
              </div>
              <div class="office-contact">
                <p>T: +84-24-3825 1072F +84-24-3826 8037</p>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="contact-form">
            <div class="form-container">
              <h2 class="form-title">We Look Forward to Hearing From You</h2>
              <p class="form-subtitle">
                Please send us your inquiries in form below or email us at
                ******@outlook.com
              </p>

              <form @submit.prevent="submitForm">
                <div class="form-row">
                  <div class="form-group">
                    <label for="firstName">Name (required)</label>
                    <input
                      type="text"
                      id="firstName"
                      v-model="form.firstName"
                      placeholder="First Name"
                      required
                    />
                  </div>
                  <div class="form-group">
                    <input
                      type="text"
                      id="lastName"
                      v-model="form.lastName"
                      placeholder="Last Name"
                    />
                  </div>
                </div>

                <div class="form-group">
                  <label for="email">Email (required)</label>
                  <input
                    type="email"
                    id="email"
                    v-model="form.email"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="phone">Phone</label>
                  <input type="tel" id="phone" v-model="form.phone" />
                </div>

                <div class="form-group">
                  <label for="organization">Organization</label>
                  <input
                    type="text"
                    id="organization"
                    v-model="form.organization"
                  />
                </div>

                <div class="form-group">
                  <label for="region">Your region (required)</label>
                  <input
                    type="text"
                    id="region"
                    v-model="form.region"
                    required
                  />
                </div>

                <button type="submit" class="submit-btn">Send</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <section class="footer-section">
      <div class="content-container">
        <div class="footer-content">
          <div class="footer-left">
            <div class="footer-logo">
              <img src="/images/logo.png" alt="CGC DIGITAL" />
              <span>CGC DIGITAL</span>
            </div>
            <p class="footer-description">
              CGC DIGITAL PROVIDES A VARIETY OF SERVICES<br />
              COVERING THE LIFECYCLE OF YOUR PROJECT
            </p>
            <div class="social-links">
              <a href="#" class="social-link">
                <img src="/images/Logo/<EMAIL>" alt="Facebook" />
              </a>
              <a href="#" class="social-link">
                <img src="/images/Vector.svg" alt="WeChat" />
              </a>
            </div>
            <div class="newsletter">
              <p>Subscribe to our newsletter:</p>
              <div class="newsletter-form">
                <input type="email" placeholder="Enter your email address" />
                <button type="submit">Send</button>
              </div>
            </div>
          </div>

          <div class="footer-right">
            <div class="footer-links">
              <div class="link-group">
                <h3>Quick Links.</h3>
                <ul>
                  <li><a href="#">SEACE</a></li>
                  <li><a href="#">SEACE DIGITAL</a></li>
                  <li><a href="#">Company</a></li>
                </ul>
              </div>

              <div class="link-group">
                <h3>Contacts</h3>
                <div class="contact-item">
                  <span class="contact-icon">📧</span>
                  <span>******@outlook.com</span>
                </div>
                <div class="contact-item">
                  <span class="contact-icon">📞</span>
                  <span>Technical support</span>
                  <span>0086-186****7676</span>
                </div>
                <div class="contact-item">
                  <span class="contact-icon">📞</span>
                  <span>Sales and business inquiries</span>
                  <span>0086-186****7676</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <p>CGC DIGITAL ©. All rights reserved.</p>
          <div class="footer-bottom-links">
            <a href="#">Term of Service</a>
            <a href="#">Privacy Policy</a>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";

const form = reactive({
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  organization: "",
  region: "",
});

const submitForm = () => {
  // Handle form submission
  console.log("Form submitted:", form);
  alert("Message sent successfully! We will get back to you soon.");

  // Reset form
  Object.keys(form).forEach((key) => {
    form[key as keyof typeof form] = "";
  });
};
</script>

<style scoped>
.contact {
  padding-top: 80px;
  min-height: 100vh;
  background: #1e1f25;
  color: white;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  position: relative;
  padding: 100px 0 60px;
  background: #1e1f25;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  z-index: 1;
}

.hero-graphics {
  position: relative;
  width: 100%;
  height: 100%;
}

.graphic-element {
  position: absolute;
  border-radius: 8px;
}

.cube-red {
  width: 60px;
  height: 60px;
  background: #ff6b6b;
  top: 20%;
  right: 30%;
  transform: rotateX(45deg) rotateY(45deg);
}

.cube-green {
  width: 80px;
  height: 80px;
  background: #51cf66;
  top: 40%;
  right: 15%;
  transform: rotateX(30deg) rotateY(30deg);
}

.cube-white {
  width: 100px;
  height: 100px;
  background: #ffffff;
  top: 30%;
  right: 45%;
  transform: rotateX(60deg) rotateY(60deg);
}

.sphere {
  width: 50px;
  height: 50px;
  background: #4c6ef5;
  border-radius: 50%;
  top: 60%;
  right: 25%;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-text {
  max-width: 500px;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1rem;
  color: #ffffff;
  opacity: 0.8;
  line-height: 1.6;
}

/* Contact Content Section */
.contact-content {
  padding: 60px 0;
  background: #1e1f25;
}

.contact-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 80px;
  align-items: start;
}

/* Contact Info */
.contact-info {
  color: white;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 30px;
  color: white;
}

.office-info {
  line-height: 1.6;
}

.office-address p {
  margin-bottom: 5px;
  color: #ffffff;
  opacity: 0.9;
}

.office-contact {
  margin-top: 20px;
}

.office-contact p {
  color: #ffffff;
  opacity: 0.9;
}

/* Contact Form */
.contact-form {
  background: #ffffff;
  border-radius: 15px;
  padding: 40px;
  color: #333;
}

.form-container {
  max-width: 100%;
}

.form-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.form-subtitle {
  color: #666;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.5;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.95rem;
  background: #f8f9fa;
  transition: border-color 0.3s, background-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #4c6ef5;
  background: #ffffff;
}

.form-group input::placeholder {
  color: #999;
}

.submit-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
  display: block;
  margin: 30px auto 0;
}

.submit-btn:hover {
  background: #ff5252;
}

/* Footer Section */
.footer-section {
  background: #1e1f25;
  padding: 60px 0 30px;
  border-top: 1px solid #333;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-left {
  color: white;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.footer-logo img {
  width: 40px;
  height: 40px;
}

.footer-logo span {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
}

.footer-description {
  color: #ccc;
  margin-bottom: 30px;
  line-height: 1.6;
  font-size: 0.9rem;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.social-link img {
  width: 24px;
  height: 24px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.social-link:hover img {
  opacity: 1;
}

.newsletter p {
  color: #ccc;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.newsletter-form {
  display: flex;
  gap: 10px;
}

.newsletter-form input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #444;
  border-radius: 5px;
  background: #2a2b31;
  color: white;
  font-size: 0.9rem;
}

.newsletter-form input::placeholder {
  color: #888;
}

.newsletter-form button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.3s;
}

.newsletter-form button:hover {
  background: #ff5252;
}

.footer-right {
  color: white;
}

.footer-links {
  display: flex;
  gap: 60px;
}

.link-group h3 {
  font-size: 1.1rem;
  margin-bottom: 20px;
  color: white;
}

.link-group ul {
  list-style: none;
  padding: 0;
}

.link-group li {
  margin-bottom: 10px;
}

.link-group a {
  color: #ccc;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s;
}

.link-group a:hover {
  color: white;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.contact-item .contact-icon {
  font-size: 1rem;
}

.contact-item span {
  color: #ccc;
}

.footer-bottom {
  border-top: 1px solid #333;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #888;
  font-size: 0.85rem;
}

.footer-bottom-links {
  display: flex;
  gap: 20px;
}

.footer-bottom-links a {
  color: #888;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-bottom-links a:hover {
  color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-links {
    flex-direction: column;
    gap: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .contact-form {
    padding: 30px 20px;
  }

  .form-title {
    font-size: 1.5rem;
  }
}
</style>

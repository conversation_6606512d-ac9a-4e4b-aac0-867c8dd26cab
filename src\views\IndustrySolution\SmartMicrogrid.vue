<template>
  <div class="smart-microgrid">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-bg-placeholder"></div>
      </div>
      <div class="hero-content">
        <div class="container">
          <h1 class="hero-title">智能微电网解决方案</h1>
          <p class="hero-subtitle">
            基于先进的能源管理技术，为您提供可靠、高效、智能的微电网系统解决方案
          </p>
          <p class="hero-description">
            集成分布式发电、储能系统、智能控制等技术，实现能源的优化配置和高效利用
          </p>
        </div>
      </div>
    </section>

    <!-- Standards Section -->
    <section class="standards">
      <div class="container">
        <h2 class="standards-title">因地制宜的体系化解决方案</h2>
        <p class="standards-subtitle">
          根据不同应用场景和需求，提供定制化的智能微电网解决方案
        </p>

        <!-- 3D Model Display -->
        <div class="model-display">
          <div class="model-container">
            <div class="model-placeholder">
              <span>智能微电网3D模型</span>
            </div>
          </div>
          <div class="model-features">
            <div class="feature-item">
              <div class="feature-icon">🔋</div>
              <span>储能系统</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">☀️</div>
              <span>光伏发电</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">💨</div>
              <span>风力发电</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🏠</div>
              <span>负载管理</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon-large">⚡</div>
            <h3>智能调度</h3>
            <p>基于AI算法的智能调度系统，实现能源供需平衡</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon-large">🔄</div>
            <h3>并离网切换</h3>
            <p>无缝并离网切换，确保供电连续性和可靠性</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon-large">📊</div>
            <h3>能效优化</h3>
            <p>实时监控和分析，持续优化系统运行效率</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon-large">🛡️</div>
            <h3>安全保护</h3>
            <p>多重安全保护机制，确保系统安全稳定运行</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Section -->
    <section class="technology">
      <div class="container">
        <div class="tech-content">
          <div class="tech-text">
            <h2>三位一体化智能管理系统</h2>
            <p>集成发电、储能、用电三大环节，实现统一监控和智能管理</p>

            <div class="tech-features">
              <div class="tech-feature">
                <h4>发电管理</h4>
                <p>多种发电方式协调控制</p>
              </div>
              <div class="tech-feature">
                <h4>储能优化</h4>
                <p>智能充放电策略优化</p>
              </div>
              <div class="tech-feature">
                <h4>负载调节</h4>
                <p>用电负载智能调节管理</p>
              </div>
            </div>
          </div>

          <div class="tech-image">
            <div class="system-placeholder">
              <span>智能管理系统</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Cases Section -->
    <section class="cases">
      <div class="container">
        <h2 class="section-title">典型应用场景</h2>

        <div class="cases-grid">
          <div class="case-item">
            <div class="case-placeholder">工业园区</div>
            <h3>工业园区</h3>
            <p>为工业园区提供可靠的电力保障和能源管理</p>
          </div>

          <div class="case-item">
            <div class="case-placeholder">商业综合体</div>
            <h3>商业综合体</h3>
            <p>商业建筑群的综合能源解决方案</p>
          </div>

          <div class="case-item">
            <div class="case-placeholder">海岛供电</div>
            <h3>海岛供电</h3>
            <p>偏远地区独立供电系统解决方案</p>
          </div>

          <div class="case-item">
            <div class="case-placeholder">数据中心</div>
            <h3>数据中心</h3>
            <p>高可靠性数据中心电力保障系统</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 智能微电网页面逻辑
</script>

<style scoped>
.smart-microgrid {
  padding-top: 130px; /* 为主导航+二级导航预留空间 */
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  opacity: 0.4;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
}

.hero-subtitle {
  font-size: 20px;
  margin-bottom: 15px;
  color: #b0b3b8;
}

.hero-description {
  font-size: 16px;
  color: #b0b3b8;
  max-width: 600px;
  margin: 0 auto;
}

/* Standards Section */
.standards {
  background: #2a2b31;
  padding: 120px 0;
}

.standards-title {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: #ffffff;
}

.standards-subtitle {
  font-size: 18px;
  text-align: center;
  color: #b0b3b8;
  margin-bottom: 80px;
}

.model-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 60px;
}

.model-container {
  flex: 1;
  text-align: center;
}

.model-placeholder {
  width: 400px;
  height: 300px;
  background: #3a3b41;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b0b3b8;
  font-size: 18px;
  border: 2px dashed #3498db;
}

.model-features {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #3a3b41;
  border-radius: 8px;
}

.feature-icon {
  font-size: 24px;
}

/* Features Section */
.features {
  padding: 120px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  background: #2a2b31;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon-large {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #3498db;
}

.feature-card p {
  color: #b0b3b8;
  line-height: 1.6;
}

/* Technology Section */
.technology {
  background: #2a2b31;
  padding: 120px 0;
}

.tech-content {
  display: flex;
  align-items: center;
  gap: 80px;
}

.tech-text {
  flex: 1;
}

.tech-text h2 {
  font-size: 36px;
  margin-bottom: 20px;
  color: #ffffff;
}

.tech-text > p {
  font-size: 18px;
  color: #b0b3b8;
  margin-bottom: 40px;
}

.tech-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-feature h4 {
  font-size: 20px;
  color: #3498db;
  margin-bottom: 8px;
}

.tech-feature p {
  color: #b0b3b8;
}

.tech-image {
  flex: 1;
}

.system-placeholder {
  width: 100%;
  height: 300px;
  background: #3a3b41;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b0b3b8;
  font-size: 18px;
  border: 2px dashed #3498db;
}

/* Cases Section */
.cases {
  padding: 120px 0;
}

.section-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 60px;
  color: #ffffff;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.case-item {
  text-align: center;
}

.case-placeholder {
  width: 100%;
  height: 200px;
  background: #3a3b41;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b0b3b8;
  font-size: 16px;
  border: 1px solid #4a4b51;
}

.case-item h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #ffffff;
}

.case-item p {
  color: #b0b3b8;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-microgrid {
    padding-top: 140px;
  }

  .hero-title {
    font-size: 32px;
  }

  .model-display {
    flex-direction: column;
    gap: 40px;
  }

  .model-features {
    flex: none;
  }

  .tech-content {
    flex-direction: column;
    gap: 40px;
  }

  .features-grid,
  .cases-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<template>
  <div class="energy-management">
    <div class="page-header">
      <div class="container">
        <h1>EMS能源管理</h1>
        <p>智能能源管理解决方案，实现能源的高效利用和优化配置</p>
      </div>
    </div>

    <div class="content-section">
      <div class="container">
        <div class="feature-grid">
          <div class="feature-card">
            <h3>能耗监测</h3>
            <p>实时监测各类设备的能耗情况，提供详细的能耗分析报告</p>
          </div>

          <div class="feature-card">
            <h3>负荷预测</h3>
            <p>基于历史数据和AI算法，准确预测未来的能源需求</p>
          </div>

          <div class="feature-card">
            <h3>优化调度</h3>
            <p>智能调度能源分配，最大化能源利用效率</p>
          </div>

          <div class="feature-card">
            <h3>成本控制</h3>
            <p>通过智能管理降低能源成本，提高经济效益</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 能源管理页面逻辑
</script>

<style scoped>
.energy-management {
  min-height: 100vh;
  background: #1e1f25;
  color: #ffffff;
  padding-top: 130px;
}

.page-header {
  background: #2a2b31;
  padding: 80px 0;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header h1 {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ffffff;
}

.page-header p {
  font-size: 18px;
  color: #b0b3b8;
  max-width: 600px;
  margin: 0 auto;
}

.content-section {
  padding: 80px 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.feature-card {
  background: #2a2b31;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #3498db;
}

.feature-card p {
  color: #b0b3b8;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .energy-management {
    padding-top: 140px;
  }

  .page-header h1 {
    font-size: 36px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}
</style>
